@echo off
"F:\\Dev\\Android_SDK_HOME\\sdk\\cmake\\3.22.1\\bin\\cmake.exe" ^
  "-Hf:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter_tools\\gradle\\src\\main\\scripts" ^
  "-DCMAKE_SYSTEM_NAME=Android" ^
  "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON" ^
  "-DCMAKE_SYSTEM_VERSION=21" ^
  "-DANDROID_PLATFORM=android-21" ^
  "-DANDROID_ABI=x86" ^
  "-DCMAKE_ANDROID_ARCH_ABI=x86" ^
  "-DANDROID_NDK=F:\\Dev\\Android_SDK_HOME\\sdk\\ndk\\26.3.11579264" ^
  "-DCMAKE_ANDROID_NDK=F:\\Dev\\Android_SDK_HOME\\sdk\\ndk\\26.3.11579264" ^
  "-DCMAKE_TOOLCHAIN_FILE=F:\\Dev\\Android_SDK_HOME\\sdk\\ndk\\26.3.11579264\\build\\cmake\\android.toolchain.cmake" ^
  "-DCMAKE_MAKE_PROGRAM=F:\\Dev\\Android_SDK_HOME\\sdk\\cmake\\3.22.1\\bin\\ninja.exe" ^
  "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\app\\intermediates\\cxx\\Debug\\8z1f4jx5\\obj\\x86" ^
  "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\app\\intermediates\\cxx\\Debug\\8z1f4jx5\\obj\\x86" ^
  "-DCMAKE_BUILD_TYPE=Debug" ^
  "-BF:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\.cxx\\Debug\\8z1f4jx5\\x86" ^
  -GNinja ^
  -Wno-dev ^
  --no-warn-unused-cli
