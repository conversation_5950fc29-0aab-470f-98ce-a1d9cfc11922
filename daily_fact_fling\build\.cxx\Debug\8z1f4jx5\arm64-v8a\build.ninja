# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: Project
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = F$:/Projetos/02$ -$ Dev/daylyFacts/daily_fact_fling/build/.cxx/Debug/8z1f4jx5/arm64-v8a/

#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\build\.cxx\Debug\8z1f4jx5\arm64-v8a" && F:\Dev\Android_SDK_HOME\sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\build\.cxx\Debug\8z1f4jx5\arm64-v8a" && F:\Dev\Android_SDK_HOME\sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -SF:\Projetos\fvm\versions\3.32.4\packages\flutter_tools\gradle\src\main\scripts -B"F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\build\.cxx\Debug\8z1f4jx5\arm64-v8a""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: F:/Projetos/02 - Dev/daylyFacts/daily_fact_fling/build/.cxx/Debug/8z1f4jx5/arm64-v8a

build all: phony

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake F$:/Dev/Android_SDK_HOME/sdk/ndk/26.3.11579264/build/cmake/android-legacy.toolchain.cmake F$:/Dev/Android_SDK_HOME/sdk/ndk/26.3.11579264/build/cmake/android.toolchain.cmake F$:/Dev/Android_SDK_HOME/sdk/ndk/26.3.11579264/build/cmake/flags.cmake F$:/Dev/Android_SDK_HOME/sdk/ndk/26.3.11579264/build/cmake/hooks/pre/Android-Clang.cmake F$:/Dev/Android_SDK_HOME/sdk/ndk/26.3.11579264/build/cmake/hooks/pre/Android-Determine.cmake F$:/Dev/Android_SDK_HOME/sdk/ndk/26.3.11579264/build/cmake/hooks/pre/Android-Initialize.cmake F$:/Dev/Android_SDK_HOME/sdk/ndk/26.3.11579264/build/cmake/hooks/pre/Android.cmake F$:/Dev/Android_SDK_HOME/sdk/ndk/26.3.11579264/build/cmake/hooks/pre/Determine-Compiler.cmake F$:/Dev/Android_SDK_HOME/sdk/ndk/26.3.11579264/build/cmake/platforms.cmake F$:/Projetos/fvm/versions/3.32.4/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt
  pool = console


#############################################
# A missing CMake input file is not an error.

build CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake F$:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake F$:/Dev/Android_SDK_HOME/sdk/ndk/26.3.11579264/build/cmake/android-legacy.toolchain.cmake F$:/Dev/Android_SDK_HOME/sdk/ndk/26.3.11579264/build/cmake/android.toolchain.cmake F$:/Dev/Android_SDK_HOME/sdk/ndk/26.3.11579264/build/cmake/flags.cmake F$:/Dev/Android_SDK_HOME/sdk/ndk/26.3.11579264/build/cmake/hooks/pre/Android-Clang.cmake F$:/Dev/Android_SDK_HOME/sdk/ndk/26.3.11579264/build/cmake/hooks/pre/Android-Determine.cmake F$:/Dev/Android_SDK_HOME/sdk/ndk/26.3.11579264/build/cmake/hooks/pre/Android-Initialize.cmake F$:/Dev/Android_SDK_HOME/sdk/ndk/26.3.11579264/build/cmake/hooks/pre/Android.cmake F$:/Dev/Android_SDK_HOME/sdk/ndk/26.3.11579264/build/cmake/hooks/pre/Determine-Compiler.cmake F$:/Dev/Android_SDK_HOME/sdk/ndk/26.3.11579264/build/cmake/platforms.cmake F$:/Projetos/fvm/versions/3.32.4/packages/flutter_tools/gradle/src/main/scripts/CMakeLists.txt: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
