import 'package:flame/game.dart';
import 'package:flame/events.dart';
import 'package:flame/components.dart';
import 'package:flutter/material.dart';
import '../utils/constants.dart';
import '../data/models/game_fact.dart';

/// Main game class for Daily Fact Fling
class DailyFactFlingGame extends FlameGame
    with HasKeyboardHandlerComponents, HasCollisionDetection {
  // Game state
  late GameState _currentState;
  int _score = 0;
  int _streak = 0;
  int _currentFactIndex = 0;
  List<GameFact> _facts = [];
  GameFact? _currentFact;

  // Game components
  late TextComponent _scoreText;
  late TextComponent _streakText;
  late TextComponent _factText;
  late RectangleComponent _leftZone;
  late RectangleComponent _rightZone;
  late TextComponent _leftLabel;
  late TextComponent _rightLabel;

  // Getters
  GameState get currentState => _currentState;
  int get score => _score;
  int get streak => _streak;
  GameFact? get currentFact => _currentFact;

  @override
  Future<void> onLoad() async {
    super.onLoad();

    // Initialize game state
    _currentState = GameState.loading;

    // Set up the game world
    await _setupGame();

    // Load initial data
    await _loadFacts();

    // Start the game
    _startGame();
  }

  /// Setup game components and UI
  Future<void> _setupGame() async {
    // Set up score display
    _scoreText = TextComponent(
      text: 'Score: $_score',
      position: Vector2(20, 20),
      textRenderer: TextPaint(
        style: const TextStyle(
          color: Colors.white,
          fontSize: 24,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
    add(_scoreText);

    // Set up streak display
    _streakText = TextComponent(
      text: 'Streak: $_streak',
      position: Vector2(20, 60),
      textRenderer: TextPaint(
        style: const TextStyle(
          color: Colors.orange,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
    add(_streakText);

    // Set up fact display area
    _factText = TextComponent(
      text: 'Loading facts...',
      position: Vector2(size.x / 2, size.y / 2),
      anchor: Anchor.center,
      textRenderer: TextPaint(
        style: const TextStyle(color: Colors.white, fontSize: 18),
      ),
    );
    add(_factText);

    // Set up left answer zone
    _leftZone = RectangleComponent(
      position: Vector2(0, size.y * 0.7),
      size: Vector2(size.x / 2, size.y * 0.3),
      paint: Paint()..color = Colors.blue.withValues(alpha: 0.3),
    );
    add(_leftZone);

    // Set up right answer zone
    _rightZone = RectangleComponent(
      position: Vector2(size.x / 2, size.y * 0.7),
      size: Vector2(size.x / 2, size.y * 0.3),
      paint: Paint()..color = Colors.green.withValues(alpha: 0.3),
    );
    add(_rightZone);

    // Add labels for the zones
    _leftLabel = TextComponent(
      text: 'Tap here for first option',
      position: Vector2(size.x / 4, size.y * 0.85),
      anchor: Anchor.center,
      textRenderer: TextPaint(
        style: const TextStyle(
          color: Colors.white,
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
    add(_leftLabel);

    _rightLabel = TextComponent(
      text: 'Tap here for second option',
      position: Vector2(size.x * 0.75, size.y * 0.85),
      anchor: Anchor.center,
      textRenderer: TextPaint(
        style: const TextStyle(
          color: Colors.white,
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
    add(_rightLabel);
  }

  /// Load facts from data source
  Future<void> _loadFacts() async {
    // TODO: Load facts from JSON file
    // For now, create sample facts
    _facts = [
      const GameFact(
        id: 'sample_1',
        text: 'Honey never spoils',
        category: FactCategory.trueOrFalse,
        options: ['True', 'False'],
        correctAnswer: 'True',
      ),
      const GameFact(
        id: 'sample_2',
        text: 'Bananas are berries',
        category: FactCategory.trueOrFalse,
        options: ['True', 'False'],
        correctAnswer: 'True',
      ),
    ];
  }

  /// Start a new game
  void _startGame() {
    _currentState = GameState.playing;
    _score = 0;
    _streak = 0;
    _currentFactIndex = 0;
    _showNextFact();
  }

  /// Show the next fact
  void _showNextFact() {
    if (_currentFactIndex >= _facts.length) {
      _endGame();
      return;
    }

    _currentFact = _facts[_currentFactIndex];
    _factText.text = _currentFact!.text;

    // Update zone labels with current options
    _leftLabel.text = _currentFact!.options.first;
    _rightLabel.text = _currentFact!.options.last;

    // Reset text color
    _factText.textRenderer = TextPaint(
      style: const TextStyle(color: Colors.white, fontSize: 18),
    );

    _currentFactIndex++;
  }

  /// Handle answer selection
  void selectAnswer(String answer) {
    if (_currentState != GameState.playing || _currentFact == null) return;

    final isCorrect = answer == _currentFact!.correctAnswer;

    if (isCorrect) {
      _score +=
          GameConstants.pointsPerCorrectAnswer +
          (_streak * GameConstants.streakMultiplier);
      _streak++;
      _showCorrectFeedback();
    } else {
      _streak = 0;
      _showIncorrectFeedback();
    }

    _updateUI();

    // Show next fact after a delay
    Future.delayed(GameConstants.feedbackDuration, () {
      _showNextFact();
    });
  }

  /// Show correct answer feedback
  void _showCorrectFeedback() {
    // TODO: Add visual/audio feedback for correct answer
    _factText.textRenderer = TextPaint(
      style: const TextStyle(
        color: Colors.green,
        fontSize: 18,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  /// Show incorrect answer feedback
  void _showIncorrectFeedback() {
    // TODO: Add visual/audio feedback for incorrect answer
    _factText.textRenderer = TextPaint(
      style: const TextStyle(
        color: Colors.red,
        fontSize: 18,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  /// Update UI elements
  void _updateUI() {
    _scoreText.text = 'Score: $_score';
    _streakText.text = 'Streak: $_streak';
  }

  /// End the current game
  void _endGame() {
    _currentState = GameState.gameOver;
    _factText.text = 'Game Over!\nFinal Score: $_score';

    // TODO: Show game over screen
    // TODO: Save high score
    // TODO: Show ads if appropriate
  }

  /// Restart the game
  void restartGame() {
    _startGame();
  }

  /// Pause the game
  void pauseGame() {
    if (_currentState == GameState.playing) {
      _currentState = GameState.paused;
    }
  }

  /// Resume the game
  void resumeGame() {
    if (_currentState == GameState.paused) {
      _currentState = GameState.playing;
    }
  }

  @override
  void update(double dt) {
    super.update(dt);

    // TODO: Add timer logic for each fact
    // TODO: Add game state specific updates
  }

  /// Handle tap/click events
  void handleTap(Vector2 position) {
    if (_currentState != GameState.playing || _currentFact == null) {
      return;
    }

    // Check if tap is in left zone (first option)
    if (position.x < size.x / 2 && position.y > size.y * 0.7) {
      selectAnswer(_currentFact!.options.first);
      return;
    }

    // Check if tap is in right zone (second option)
    if (position.x >= size.x / 2 && position.y > size.y * 0.7) {
      selectAnswer(_currentFact!.options.last);
      return;
    }
  }
}
