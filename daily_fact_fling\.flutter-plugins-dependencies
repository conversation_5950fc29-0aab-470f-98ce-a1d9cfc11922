{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "audioplayers_darwin", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\audioplayers_darwin-6.3.0\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_mobile_ads", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\google_mobile_ads-5.3.1\\\\", "native_build": true, "dependencies": ["webview_flutter_wkwebview"], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\path_provider_foundation-2.4.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\shared_preferences_foundation-2.5.4\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "webview_flutter_wkwebview", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\webview_flutter_wkwebview-3.22.0\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}], "android": [{"name": "audioplayers_android", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\audioplayers_android-5.2.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "google_mobile_ads", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\google_mobile_ads-5.3.1\\\\", "native_build": true, "dependencies": ["webview_flutter_android"], "dev_dependency": false}, {"name": "path_provider_android", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\path_provider_android-2.2.17\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_android", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\shared_preferences_android-2.4.10\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "webview_flutter_android", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\webview_flutter_android-4.7.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "macos": [{"name": "audioplayers_darwin", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\audioplayers_darwin-6.3.0\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\path_provider_foundation-2.4.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\shared_preferences_foundation-2.5.4\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "webview_flutter_wkwebview", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\webview_flutter_wkwebview-3.22.0\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}], "linux": [{"name": "audioplayers_linux", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\audioplayers_linux-4.2.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_linux", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\path_provider_linux-2.2.1\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_linux", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\shared_preferences_linux-2.4.1\\\\", "native_build": false, "dependencies": ["path_provider_linux"], "dev_dependency": false}], "windows": [{"name": "audioplayers_windows", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\audioplayers_windows-4.2.1\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_windows", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\path_provider_windows-2.3.0\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_windows", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\shared_preferences_windows-2.4.1\\\\", "native_build": false, "dependencies": ["path_provider_windows"], "dev_dependency": false}], "web": [{"name": "audioplayers_web", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\audioplayers_web-5.1.1\\\\", "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_web", "path": "F:\\\\Projetos\\\\PUB_CACHE\\\\hosted\\\\pub.dev\\\\shared_preferences_web-2.4.3\\\\", "dependencies": [], "dev_dependency": false}]}, "dependencyGraph": [{"name": "audioplayers", "dependencies": ["audioplayers_android", "audioplayers_darwin", "audioplayers_linux", "audioplayers_web", "audioplayers_windows", "path_provider"]}, {"name": "audioplayers_android", "dependencies": []}, {"name": "audioplayers_darwin", "dependencies": []}, {"name": "audioplayers_linux", "dependencies": []}, {"name": "audioplayers_web", "dependencies": []}, {"name": "audioplayers_windows", "dependencies": []}, {"name": "google_mobile_ads", "dependencies": ["webview_flutter_android", "webview_flutter_wkwebview", "webview_flutter"]}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "webview_flutter", "dependencies": ["webview_flutter_android", "webview_flutter_wkwebview"]}, {"name": "webview_flutter_android", "dependencies": []}, {"name": "webview_flutter_wkwebview", "dependencies": []}], "date_created": "2025-06-18 16:57:45.428838", "version": "3.32.4", "swift_package_manager_enabled": {"ios": false, "macos": false}}