import 'dart:convert';
import 'dart:math';
import 'package:flutter/services.dart';
// import 'package:shared_preferences/shared_preferences.dart'; // Temporariamente comentado
import '../models/game_fact.dart';
import '../../utils/constants.dart';

/// Service for managing game data and persistence
class GameDataService {
  List<GameFact> _facts = [];
  // SharedPreferences? _prefs; // Temporariamente comentado

  /// Initialize the service
  Future<void> initialize() async {
    // _prefs = await SharedPreferences.getInstance(); // Temporariamente comentado
    await _loadFacts();
  }

  /// Load facts from JSON file
  Future<void> _loadFacts() async {
    try {
      final String jsonString = await rootBundle.loadString(
        AssetPaths.factsData,
      );
      final List<dynamic> jsonList = json.decode(jsonString);

      _facts = jsonList.map((json) => GameFact.fromJson(json)).toList();
    } catch (e) {
      // If loading fails, use default facts
      _facts = _getDefaultFacts();
    }
  }

  /// Get a random selection of facts for a game session
  List<GameFact> getRandomFacts({int count = 20}) {
    if (_facts.isEmpty) {
      return _getDefaultFacts();
    }

    final random = Random();
    final shuffled = List<GameFact>.from(_facts)..shuffle(random);

    return shuffled.take(count).toList();
  }

  /// Get facts by category
  List<GameFact> getFactsByCategory(FactCategory category) {
    return _facts.where((fact) => fact.category == category).toList();
  }

  /// Get facts by difficulty
  List<GameFact> getFactsByDifficulty(DifficultyLevel difficulty) {
    return _facts.where((fact) => fact.difficulty == difficulty).toList();
  }

  /// Save high score (temporariamente desabilitado)
  Future<void> saveHighScore(int score) async {
    // await _prefs?.setInt(GameConstants.highScoreKey, score);
  }

  /// Get high score (temporariamente desabilitado)
  int getHighScore() {
    // return _prefs?.getInt(GameConstants.highScoreKey) ?? 0;
    return 0;
  }

  /// Save total games played (temporariamente desabilitado)
  Future<void> incrementGamesPlayed() async {
    // final current = getTotalGamesPlayed();
    // await _prefs?.setInt(GameConstants.totalGamesPlayedKey, current + 1);
  }

  /// Get total games played (temporariamente desabilitado)
  int getTotalGamesPlayed() {
    // return _prefs?.getInt(GameConstants.totalGamesPlayedKey) ?? 0;
    return 0;
  }

  /// Save sound setting (temporariamente desabilitado)
  Future<void> setSoundEnabled(bool enabled) async {
    // await _prefs?.setBool(GameConstants.soundEnabledKey, enabled);
  }

  /// Get sound setting (temporariamente desabilitado)
  bool isSoundEnabled() {
    // return _prefs?.getBool(GameConstants.soundEnabledKey) ?? true;
    return true;
  }

  /// Save music setting (temporariamente desabilitado)
  Future<void> setMusicEnabled(bool enabled) async {
    // await _prefs?.setBool(GameConstants.musicEnabledKey, enabled);
  }

  /// Get music setting (temporariamente desabilitado)
  bool isMusicEnabled() {
    // return _prefs?.getBool(GameConstants.musicEnabledKey) ?? true;
    return true;
  }

  /// Get default facts if loading from file fails
  List<GameFact> _getDefaultFacts() {
    return [
      const GameFact(
        id: 'default_001',
        text:
            'Honey never spoils. Archaeologists have found pots of honey in ancient Egyptian tombs that are over 3,000 years old and still perfectly edible.',
        category: FactCategory.trueOrFalse,
        options: ['True', 'False'],
        correctAnswer: 'True',
        explanation:
            'Honey\'s low moisture content and acidic pH create an environment where bacteria cannot survive.',
        difficulty: DifficultyLevel.easy,
      ),
      const GameFact(
        id: 'default_002',
        text: 'Bananas are berries, but strawberries aren\'t.',
        category: FactCategory.trueOrFalse,
        options: ['True', 'False'],
        correctAnswer: 'True',
        explanation:
            'Botanically, berries must have seeds inside their flesh. Bananas qualify, but strawberries have seeds on the outside.',
        difficulty: DifficultyLevel.medium,
      ),
      const GameFact(
        id: 'default_003',
        text: 'A group of flamingos is called a \'flamboyance\'.',
        category: FactCategory.trueOrFalse,
        options: ['True', 'False'],
        correctAnswer: 'True',
        explanation:
            'The collective noun for flamingos is indeed \'flamboyance\', which perfectly suits their colorful appearance.',
        difficulty: DifficultyLevel.easy,
      ),
      const GameFact(
        id: 'default_004',
        text: 'Octopuses have three hearts and blue blood.',
        category: FactCategory.trueOrFalse,
        options: ['True', 'False'],
        correctAnswer: 'True',
        explanation:
            'Two hearts pump blood to the gills, one pumps to the body. Their blood is blue due to copper-based hemocyanin.',
        difficulty: DifficultyLevel.medium,
      ),
      const GameFact(
        id: 'default_005',
        text: 'Wombat poop is cube-shaped.',
        category: FactCategory.funnyOrSerious,
        options: ['Funny', 'Serious'],
        correctAnswer: 'Serious',
        explanation:
            'This is a real scientific fact! Wombats produce cube-shaped feces due to their unique digestive system.',
        difficulty: DifficultyLevel.medium,
      ),
    ];
  }

  /// Get all available facts
  List<GameFact> get allFacts => List.unmodifiable(_facts);

  /// Get total number of facts
  int get totalFactsCount => _facts.length;

  /// Check if facts are loaded
  bool get isInitialized => _facts.isNotEmpty;
}
