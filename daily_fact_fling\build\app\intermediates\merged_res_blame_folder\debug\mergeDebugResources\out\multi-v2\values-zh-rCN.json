{"logs": [{"outputFile": "com.example.dailyfactfling.daily_fact_fling.app-mergeDebugResources-50:/values-zh-rCN/values-zh-rCN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e20001cc41f5487e719977c4b5116946\\transformed\\jetified-play-services-base-18.0.0\\res\\values-zh-rCN\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,419,530,628,729,841,939,1027,1131,1228,1354,1465,1565,1669,1721,1774", "endColumns": "96,124,110,97,100,111,97,87,103,96,125,110,99,103,51,52,69", "endOffsets": "293,418,529,627,728,840,938,1026,1130,1227,1353,1464,1564,1668,1720,1773,1843"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3326,3427,3556,3671,3773,3878,3994,4096,4287,4395,4496,4626,4741,4845,4953,5009,5066", "endColumns": "100,128,114,101,104,115,101,91,107,100,129,114,103,107,55,56,73", "endOffsets": "3422,3551,3666,3768,3873,3989,4091,4183,4390,4491,4621,4736,4840,4948,5004,5061,5135"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e5aa58ad971ef0b034990717d52ef9e6\\transformed\\jetified-play-services-ads-23.6.0\\res\\values-zh-rCN\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "203,242,285,332,392,453,521,581,652,724,810,860,911,980,1038,1070,1113,1143,1173,1207,1247,1279", "endColumns": "38,42,46,59,60,67,59,70,71,85,49,50,68,57,31,42,29,29,33,39,31,55", "endOffsets": "241,284,331,391,452,520,580,651,723,809,859,910,979,1037,1069,1112,1142,1172,1206,1246,1278,1334"}, "to": {"startLines": "60,61,62,64,65,66,67,68,69,70,71,72,73,74,76,77,78,79,80,81,82,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5656,5699,5746,5867,5931,5996,6068,6132,6207,6283,6373,6427,6482,6555,6737,6773,6820,6854,6888,6926,6970,7511", "endColumns": "42,46,50,63,64,71,63,74,75,89,53,54,72,61,35,46,33,33,37,43,35,59", "endOffsets": "5694,5741,5792,5926,5991,6063,6127,6202,6278,6368,6422,6477,6550,6612,6768,6815,6849,6883,6921,6965,7001,7566"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,248,342,436,529,623,719", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "142,243,337,431,524,618,714,815"}, "to": {"startLines": "29,30,31,32,33,34,35,84", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2662,2754,2855,2949,3043,3136,3230,7085", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "2749,2850,2944,3038,3131,3225,3321,7181"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0c69679757972620720ec039d7103818\\transformed\\browser-1.8.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,138,230,331", "endColumns": "82,91,100,92", "endOffsets": "133,225,326,419"}, "to": {"startLines": "55,57,58,59", "startColumns": "4,4,4,4", "startOffsets": "5206,5370,5462,5563", "endColumns": "82,91,100,92", "endOffsets": "5284,5457,5558,5651"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1186d146da5ef23629d7bf94e5a0d382\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-zh-rCN\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "94", "endOffsets": "293"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4188", "endColumns": "98", "endOffsets": "4282"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b83b8b00b8346c9e7414a1f1298f055d\\transformed\\preference-1.2.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,171,252,322,442,610,690", "endColumns": "65,80,69,119,167,79,76", "endOffsets": "166,247,317,437,605,685,762"}, "to": {"startLines": "54,56,63,75,85,86,87", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5140,5289,5797,6617,7186,7354,7434", "endColumns": "65,80,69,119,167,79,76", "endOffsets": "5201,5365,5862,6732,7349,7429,7506"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a295c1332cd792405fffabf7b4bbac54\\transformed\\appcompat-1.2.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,832,923,1016,1113,1209,1303,1396,1491,1583,1674,1765,1843,1939,2034,2129,2226,2322,2420,2568,2662", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "195,290,390,472,569,675,752,827,918,1011,1108,1204,1298,1391,1486,1578,1669,1760,1838,1934,2029,2124,2221,2317,2415,2563,2657,2736"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,83", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,832,923,1016,1113,1209,1303,1396,1491,1583,1674,1765,1843,1939,2034,2129,2226,2322,2420,2568,7006", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "195,290,390,472,569,675,752,827,918,1011,1108,1204,1298,1391,1486,1578,1669,1760,1838,1934,2029,2124,2221,2317,2415,2563,2657,7080"}}]}]}