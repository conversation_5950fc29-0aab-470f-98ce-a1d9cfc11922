daily_fact_fling
3.8
file:///F:/Projetos/02%20-%20Dev/daylyFacts/daily_fact_fling/
file:///F:/Projetos/02%20-%20Dev/daylyFacts/daily_fact_fling/lib/
async
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/async-2.13.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/async-2.13.0/lib/
boolean_selector
3.1
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/boolean_selector-2.1.2/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/boolean_selector-2.1.2/lib/
characters
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/characters-1.4.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/characters-1.4.0/lib/
clock
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/clock-1.1.2/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/clock-1.1.2/lib/
collection
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/collection-1.19.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/collection-1.19.1/lib/
cupertino_icons
3.1
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/cupertino_icons-1.0.8/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/cupertino_icons-1.0.8/lib/
fake_async
3.3
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/fake_async-1.3.3/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/fake_async-1.3.3/lib/
flame
3.6
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/flame-1.29.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/flame-1.29.0/lib/
flutter_lints
3.5
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/flutter_lints-5.0.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/flutter_lints-5.0.0/lib/
leak_tracker
3.2
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/leak_tracker-10.0.9/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/leak_tracker-10.0.9/lib/
leak_tracker_flutter_testing
3.2
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/lib/
leak_tracker_testing
3.2
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/leak_tracker_testing-3.0.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/leak_tracker_testing-3.0.1/lib/
lints
3.6
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/lints-5.1.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/lints-5.1.1/lib/
matcher
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/matcher-0.12.17/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/matcher-0.12.17/lib/
material_color_utilities
2.17
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/material_color_utilities-0.11.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/material_color_utilities-0.11.1/lib/
meta
2.12
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/meta-1.16.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/meta-1.16.0/lib/
nested
2.12
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/nested-1.0.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/nested-1.0.0/lib/
ordered_set
3.6
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/ordered_set-8.0.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/ordered_set-8.0.0/lib/
path
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path-1.9.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path-1.9.1/lib/
provider
2.12
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/provider-6.1.5/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/provider-6.1.5/lib/
source_span
3.1
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/source_span-1.10.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/source_span-1.10.1/lib/
stack_trace
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/stack_trace-1.12.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/stack_trace-1.12.1/lib/
stream_channel
3.3
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/stream_channel-2.1.4/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/stream_channel-2.1.4/lib/
string_scanner
3.1
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/string_scanner-1.4.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/string_scanner-1.4.1/lib/
term_glyph
3.1
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/term_glyph-1.2.2/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/term_glyph-1.2.2/lib/
test_api
3.5
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/test_api-0.7.4/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/test_api-0.7.4/lib/
vector_math
2.14
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/vector_math-2.1.4/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/vector_math-2.1.4/lib/
vm_service
3.3
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/vm_service-15.0.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/vm_service-15.0.0/lib/
sky_engine
3.7
file:///f:/Projetos/fvm/versions/3.32.4/bin/cache/pkg/sky_engine/
file:///f:/Projetos/fvm/versions/3.32.4/bin/cache/pkg/sky_engine/lib/
flutter
3.7
file:///f:/Projetos/fvm/versions/3.32.4/packages/flutter/
file:///f:/Projetos/fvm/versions/3.32.4/packages/flutter/lib/
flutter_test
3.7
file:///f:/Projetos/fvm/versions/3.32.4/packages/flutter_test/
file:///f:/Projetos/fvm/versions/3.32.4/packages/flutter_test/lib/
2
