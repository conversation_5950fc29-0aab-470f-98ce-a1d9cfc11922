daily_fact_fling
3.8
file:///F:/Projetos/02%20-%20Dev/daylyFacts/daily_fact_fling/
file:///F:/Projetos/02%20-%20Dev/daylyFacts/daily_fact_fling/lib/
async
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/async-2.13.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/async-2.13.0/lib/
audioplayers
3.6
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/audioplayers-6.5.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/audioplayers-6.5.0/lib/
audioplayers_android
3.6
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/audioplayers_android-5.2.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/audioplayers_android-5.2.1/lib/
audioplayers_darwin
3.6
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/audioplayers_darwin-6.3.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/audioplayers_darwin-6.3.0/lib/
audioplayers_linux
3.6
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/audioplayers_linux-4.2.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/audioplayers_linux-4.2.1/lib/
audioplayers_platform_interface
3.6
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/audioplayers_platform_interface-7.1.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/audioplayers_platform_interface-7.1.1/lib/
audioplayers_web
3.6
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/audioplayers_web-5.1.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/audioplayers_web-5.1.1/lib/
audioplayers_windows
3.6
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/audioplayers_windows-4.2.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/audioplayers_windows-4.2.1/lib/
boolean_selector
3.1
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/boolean_selector-2.1.2/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/boolean_selector-2.1.2/lib/
characters
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/characters-1.4.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/characters-1.4.0/lib/
clock
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/clock-1.1.2/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/clock-1.1.2/lib/
collection
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/collection-1.19.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/collection-1.19.1/lib/
crypto
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/crypto-3.0.6/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/crypto-3.0.6/lib/
cupertino_icons
3.1
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/cupertino_icons-1.0.8/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/cupertino_icons-1.0.8/lib/
fake_async
3.3
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/fake_async-1.3.3/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/fake_async-1.3.3/lib/
ffi
3.7
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/ffi-2.1.4/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/ffi-2.1.4/lib/
file
3.0
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/file-7.0.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/file-7.0.1/lib/
fixnum
3.1
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/fixnum-1.1.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/fixnum-1.1.1/lib/
flame
3.6
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/flame-1.29.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/flame-1.29.0/lib/
flutter_lints
3.5
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/flutter_lints-5.0.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/flutter_lints-5.0.0/lib/
google_mobile_ads
2.17
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/google_mobile_ads-5.3.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/google_mobile_ads-5.3.1/lib/
http
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/http-1.4.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/http-1.4.0/lib/
http_parser
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/http_parser-4.1.2/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/http_parser-4.1.2/lib/
leak_tracker
3.2
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/leak_tracker-10.0.9/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/leak_tracker-10.0.9/lib/
leak_tracker_flutter_testing
3.2
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/lib/
leak_tracker_testing
3.2
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/leak_tracker_testing-3.0.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/leak_tracker_testing-3.0.1/lib/
lints
3.6
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/lints-5.1.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/lints-5.1.1/lib/
matcher
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/matcher-0.12.17/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/matcher-0.12.17/lib/
material_color_utilities
2.17
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/material_color_utilities-0.11.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/material_color_utilities-0.11.1/lib/
meta
2.12
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/meta-1.16.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/meta-1.16.0/lib/
nested
2.12
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/nested-1.0.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/nested-1.0.0/lib/
ordered_set
3.6
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/ordered_set-8.0.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/ordered_set-8.0.0/lib/
path
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path-1.9.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path-1.9.1/lib/
path_provider
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path_provider-2.1.5/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path_provider-2.1.5/lib/
path_provider_android
3.6
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path_provider_android-2.2.17/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path_provider_android-2.2.17/lib/
path_provider_foundation
3.3
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path_provider_foundation-2.4.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path_provider_foundation-2.4.1/lib/
path_provider_linux
2.19
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path_provider_linux-2.2.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path_provider_linux-2.2.1/lib/
path_provider_platform_interface
3.0
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path_provider_platform_interface-2.1.2/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/
path_provider_windows
3.2
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path_provider_windows-2.3.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/path_provider_windows-2.3.0/lib/
platform
3.2
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/platform-3.1.6/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/platform-3.1.6/lib/
plugin_platform_interface
3.0
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/plugin_platform_interface-2.1.8/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/
provider
2.12
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/provider-6.1.5/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/provider-6.1.5/lib/
shared_preferences
3.5
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences-2.5.3/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences-2.5.3/lib/
shared_preferences_android
3.6
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences_android-2.4.10/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences_android-2.4.10/lib/
shared_preferences_foundation
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences_foundation-2.5.4/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib/
shared_preferences_linux
3.3
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences_linux-2.4.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences_linux-2.4.1/lib/
shared_preferences_platform_interface
3.2
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/
shared_preferences_web
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences_web-2.4.3/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences_web-2.4.3/lib/
shared_preferences_windows
3.3
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences_windows-2.4.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/shared_preferences_windows-2.4.1/lib/
source_span
3.1
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/source_span-1.10.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/source_span-1.10.1/lib/
sprintf
2.12
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/sprintf-7.0.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/sprintf-7.0.0/lib/
stack_trace
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/stack_trace-1.12.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/stack_trace-1.12.1/lib/
stream_channel
3.3
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/stream_channel-2.1.4/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/stream_channel-2.1.4/lib/
string_scanner
3.1
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/string_scanner-1.4.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/string_scanner-1.4.1/lib/
synchronized
3.7
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/synchronized-3.3.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/synchronized-3.3.1/lib/
term_glyph
3.1
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/term_glyph-1.2.2/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/term_glyph-1.2.2/lib/
test_api
3.5
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/test_api-0.7.4/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/test_api-0.7.4/lib/
typed_data
3.5
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/typed_data-1.4.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/typed_data-1.4.0/lib/
uuid
3.0
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/uuid-4.5.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/uuid-4.5.1/lib/
vector_math
2.14
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/vector_math-2.1.4/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/vector_math-2.1.4/lib/
vm_service
3.3
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/vm_service-15.0.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/vm_service-15.0.0/lib/
web
3.4
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/web-1.1.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/web-1.1.1/lib/
webview_flutter
3.6
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/webview_flutter-4.13.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/webview_flutter-4.13.0/lib/
webview_flutter_android
3.6
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/webview_flutter_android-4.7.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/webview_flutter_android-4.7.0/lib/
webview_flutter_platform_interface
3.6
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/lib/
webview_flutter_wkwebview
3.5
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/lib/
xdg_directories
3.3
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/xdg_directories-1.1.0/
file:///F:/Projetos/PUB_CACHE/hosted/pub.dev/xdg_directories-1.1.0/lib/
sky_engine
3.7
file:///f:/Projetos/fvm/versions/3.32.4/bin/cache/pkg/sky_engine/
file:///f:/Projetos/fvm/versions/3.32.4/bin/cache/pkg/sky_engine/lib/
flutter
3.7
file:///f:/Projetos/fvm/versions/3.32.4/packages/flutter/
file:///f:/Projetos/fvm/versions/3.32.4/packages/flutter/lib/
flutter_test
3.7
file:///f:/Projetos/fvm/versions/3.32.4/packages/flutter_test/
file:///f:/Projetos/fvm/versions/3.32.4/packages/flutter_test/lib/
flutter_web_plugins
3.7
file:///f:/Projetos/fvm/versions/3.32.4/packages/flutter_web_plugins/
file:///f:/Projetos/fvm/versions/3.32.4/packages/flutter_web_plugins/lib/
2
