import 'package:flutter/material.dart';
import 'package:flame/game.dart';
import 'package:provider/provider.dart';
import '../../game/daily_fact_fling_game.dart';
import '../../main.dart';

/// Game screen that hosts the Flame game
class GameScreen extends StatefulWidget {
  const GameScreen({super.key});

  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen> {
  late DailyFactFlingGame game;

  @override
  void initState() {
    super.initState();

    // Initialize game
    game = DailyFactFlingGame();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GestureDetector(
        onTap: () {
          // Simular toque na zona esquerda para teste
          game.handleTap(Vector2(100, 500));
        },
        onTapDown: (details) {
          // Usar coordenadas locais diretamente
          final position = details.localPosition;
          game.handleTap(Vector2(position.dx, position.dy));
        },
        child: GameWidget<DailyFactFlingGame>.controlled(
          gameFactory: () => game,
          overlayBuilderMap: {
            'PauseMenu': (context, game) => _buildPauseMenu(context, game),
            'GameOverMenu': (context, game) =>
                _buildGameOverMenu(context, game),
            'GameHUD': (context, game) => _buildGameHUD(context, game),
          },
          initialActiveOverlays: const ['GameHUD'],
        ),
      ),
    );
  }

  /// Build the game HUD overlay
  Widget _buildGameHUD(BuildContext context, DailyFactFlingGame game) {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Top HUD
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Score and streak
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Score: ${game.score}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        shadows: [
                          Shadow(
                            offset: Offset(1, 1),
                            blurRadius: 2,
                            color: Colors.black54,
                          ),
                        ],
                      ),
                    ),
                    Text(
                      'Streak: ${game.streak}',
                      style: const TextStyle(
                        color: Colors.orange,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        shadows: [
                          Shadow(
                            offset: Offset(1, 1),
                            blurRadius: 2,
                            color: Colors.black54,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                // Pause button
                IconButton(
                  onPressed: () => _pauseGame(context, game),
                  icon: const Icon(Icons.pause, color: Colors.white, size: 32),
                ),
              ],
            ),

            const Spacer(),

            // Draggable fact and drop zones
            if (game.currentFact != null)
              Expanded(
                child: Column(
                  children: [
                    // Instruction text
                    Text(
                      'Drag the fact to the correct answer zone:',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 20),

                    // Draggable fact card
                    Expanded(
                      flex: 2,
                      child: Center(
                        child: Draggable<String>(
                          data: game.currentFact!.text,
                          feedback: Material(
                            color: Colors.transparent,
                            child: Container(
                              width: 280,
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.9),
                                borderRadius: BorderRadius.circular(12),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.3),
                                    blurRadius: 8,
                                    offset: const Offset(0, 4),
                                  ),
                                ],
                              ),
                              child: Text(
                                game.currentFact!.text,
                                style: const TextStyle(
                                  color: Colors.black,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ),
                          childWhenDragging: Container(
                            width: 300,
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.grey.withValues(alpha: 0.5),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Colors.white.withValues(alpha: 0.3),
                                width: 2,
                              ),
                            ),
                            child: Text(
                              game.currentFact!.text,
                              style: TextStyle(
                                color: Colors.white.withValues(alpha: 0.5),
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          child: Container(
                            width: 300,
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.2),
                                  blurRadius: 4,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Text(
                              game.currentFact!.text,
                              style: const TextStyle(
                                color: Colors.black,
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 20),

                    // Drop zones
                    Expanded(
                      flex: 1,
                      child: Row(
                        children: [
                          // Left drop zone
                          Expanded(
                            child: DragTarget<String>(
                              onAcceptWithDetails: (details) {
                                game.selectAnswer(
                                  game.currentFact!.options.first,
                                );
                              },
                              builder: (context, candidateData, rejectedData) {
                                final isHovering = candidateData.isNotEmpty;
                                return Container(
                                  margin: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: isHovering
                                        ? Colors.blue.withValues(alpha: 0.6)
                                        : Colors.blue.withValues(alpha: 0.3),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: Colors.blue,
                                      width: isHovering ? 3 : 2,
                                    ),
                                  ),
                                  child: Center(
                                    child: Text(
                                      game.currentFact!.options.first,
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),

                          // Right drop zone
                          Expanded(
                            child: DragTarget<String>(
                              onAcceptWithDetails: (details) {
                                game.selectAnswer(
                                  game.currentFact!.options.last,
                                );
                              },
                              builder: (context, candidateData, rejectedData) {
                                final isHovering = candidateData.isNotEmpty;
                                return Container(
                                  margin: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: isHovering
                                        ? Colors.green.withValues(alpha: 0.6)
                                        : Colors.green.withValues(alpha: 0.3),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: Colors.green,
                                      width: isHovering ? 3 : 2,
                                    ),
                                  ),
                                  child: Center(
                                    child: Text(
                                      game.currentFact!.options.last,
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// Build the pause menu overlay
  Widget _buildPauseMenu(BuildContext context, DailyFactFlingGame game) {
    return Container(
      color: Colors.black.withValues(alpha: 0.8),
      child: Center(
        child: Card(
          margin: const EdgeInsets.all(32),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Game Paused',
                  style: Theme.of(context).textTheme.headlineMedium,
                ),
                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => _resumeGame(context, game),
                    child: const Text('Resume'),
                  ),
                ),
                const SizedBox(height: 12),
                SizedBox(
                  width: double.infinity,
                  child: TextButton(
                    onPressed: () => _exitToMenu(context),
                    child: const Text('Exit to Menu'),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Build the game over menu overlay
  Widget _buildGameOverMenu(BuildContext context, DailyFactFlingGame game) {
    return Container(
      color: Colors.black.withValues(alpha: 0.8),
      child: Center(
        child: Card(
          margin: const EdgeInsets.all(32),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Game Over!',
                  style: Theme.of(context).textTheme.headlineMedium,
                ),
                const SizedBox(height: 16),
                Text(
                  'Final Score: ${game.score}',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => _restartGame(context, game),
                    child: const Text('Play Again'),
                  ),
                ),
                const SizedBox(height: 12),
                SizedBox(
                  width: double.infinity,
                  child: TextButton(
                    onPressed: () => _exitToMenu(context),
                    child: const Text('Main Menu'),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _pauseGame(BuildContext context, DailyFactFlingGame game) {
    game.pauseGame();
    game.overlays.add('PauseMenu');
  }

  void _resumeGame(BuildContext context, DailyFactFlingGame game) {
    game.overlays.remove('PauseMenu');
    game.resumeGame();
  }

  void _restartGame(BuildContext context, DailyFactFlingGame game) {
    game.overlays.remove('GameOverMenu');
    game.restartGame();
  }

  void _exitToMenu(BuildContext context) {
    final gameStateNotifier = context.read<GameStateNotifier>();
    gameStateNotifier.endGame();
    Navigator.of(context).pop();
  }

  @override
  void dispose() {
    // Don't access context in dispose - it's unsafe
    super.dispose();
  }
}
