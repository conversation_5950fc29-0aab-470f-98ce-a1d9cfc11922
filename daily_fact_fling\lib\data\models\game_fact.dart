import '../../utils/constants.dart';

/// Model representing a game fact
class GameFact {
  final String id;
  final String text;
  final FactCategory category;
  final List<String> options;
  final String correctAnswer;
  final String? explanation;
  final DifficultyLevel difficulty;

  const GameFact({
    required this.id,
    required this.text,
    required this.category,
    required this.options,
    required this.correctAnswer,
    this.explanation,
    this.difficulty = DifficultyLevel.medium,
  });

  /// Create GameFact from JSON
  factory GameFact.fromJson(Map<String, dynamic> json) {
    return GameFact(
      id: json['id'] as String,
      text: json['text'] as String,
      category: FactCategory.values.firstWhere(
        (e) => e.toString().split('.').last == json['category'],
        orElse: () => FactCategory.trueOrFalse,
      ),
      options: List<String>.from(json['options'] as List),
      correctAnswer: json['correct_answer'] as String,
      explanation: json['explanation'] as String?,
      difficulty: DifficultyLevel.values.firstWhere(
        (e) => e.toString().split('.').last == json['difficulty'],
        orElse: () => DifficultyLevel.medium,
      ),
    );
  }

  /// Convert GameFact to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'category': category.toString().split('.').last,
      'options': options,
      'correct_answer': correctAnswer,
      'explanation': explanation,
      'difficulty': difficulty.toString().split('.').last,
    };
  }

  /// Create a copy with modified properties
  GameFact copyWith({
    String? id,
    String? text,
    FactCategory? category,
    List<String>? options,
    String? correctAnswer,
    String? explanation,
    DifficultyLevel? difficulty,
  }) {
    return GameFact(
      id: id ?? this.id,
      text: text ?? this.text,
      category: category ?? this.category,
      options: options ?? this.options,
      correctAnswer: correctAnswer ?? this.correctAnswer,
      explanation: explanation ?? this.explanation,
      difficulty: difficulty ?? this.difficulty,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GameFact && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'GameFact(id: $id, text: $text, category: $category, correctAnswer: $correctAnswer)';
  }
}

/// Extension for FactCategory
extension FactCategoryExtension on FactCategory {
  String get displayName {
    switch (this) {
      case FactCategory.trueOrFalse:
        return 'True or False';
      case FactCategory.funnyOrSerious:
        return 'Funny or Serious';
      case FactCategory.oldOrNew:
        return 'Old or New';
      case FactCategory.scienceOrNot:
        return 'Science or Not';
    }
  }

  List<String> get defaultOptions {
    switch (this) {
      case FactCategory.trueOrFalse:
        return ['True', 'False'];
      case FactCategory.funnyOrSerious:
        return ['Funny', 'Serious'];
      case FactCategory.oldOrNew:
        return ['Old', 'New'];
      case FactCategory.scienceOrNot:
        return ['Science', 'Not Science'];
    }
  }
}
