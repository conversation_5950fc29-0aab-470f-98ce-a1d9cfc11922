{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "F:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "F:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "F:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/bin/ctest.exe", "root": "F:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-5750c7c47d1cb9c3aa1e.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-1a521aebdd3b6a05565d.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-c20d89a0aaf8aa57e744.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-1a521aebdd3b6a05565d.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-c20d89a0aaf8aa57e744.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-5750c7c47d1cb9c3aa1e.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}