Especificação para um Agente de Desenvolvimento de Aplicativos: "Daily Fact Fling"
Esta especificação detalha os requisitos para um agente (seja ele uma IA, um desenvolvedor individual ou uma equipe) criar o aplicativo de jogo "Daily Fact Fling" usando Flutter e Flame, com monetização via Google AdMob. O objetivo é um MVP (Produto M<PERSON>) funcional, divertido e pronto para lançamento rápido.

1. Visão Geral do Aplicativo
Nome do Aplicativo: Daily Fact Fling
Plataformas Alvo: Android e iOS (web e desktop como bônus, dada a capacidade do Flutter)
Conceito: Um jogo hyper-casual e de reação rápida onde os jogadores são apresentados a fatos interessantes e devem classificá-los ("fling") rapidamente para categorias predefinidas (ex: "Verdadeiro", "Falso", "Engraçado", "Mente-Aberta") antes que um tempo limite expire. O objetivo é maximizar a pontuação e a sequência de acertos.
Monetização: Google AdMob (com foco em anúncios recompensados e intersticiais).
2. Recursos Essenciais do MVP
O aplicativo deve incluir os seguintes recursos para sua primeira versão:

2.1. Tela de Início (Main Menu)
Título do Jogo: "Daily Fact Fling" ou similar.
Botão "Jogar": Inicia uma nova rodada do jogo.
Botão "Como Jogar": Abre uma tela simples com instruções textuais ou visuais da mecânica de "fling".
Botão "Pontuações" (opcional para MVP, mas desejável): Mostra a maior pontuação do usuário.
2.2. Loop de Gameplay Principal
Exibição de Fatos: Um fato curto e interessante deve aparecer no centro da tela.
Timer: Um cronômetro visível (progress bar ou numérico) deve indicar o tempo restante para o julgamento. O tempo deve diminuir a cada fato ou aumentar de dificuldade progressivamente.
Áreas de Julgamento ("Fling Zones"): Duas ou mais áreas claramente marcadas na tela (ex: esquerda/direita, topo/base, ou ícones de categoria) para onde o jogador deve "arremessar" o fato. As categorias devem ser claras e fixas para cada fato (ex: para um fato, as opções podem ser "Verdadeiro" e "Falso"; para outro, "Engraçado" e "Sério").
Mecânica de "Fling": O jogador deve tocar no fato e arrastá-lo (soltá-lo) para a área de julgamento correta.
Feedback Visual e Sonoro:
Acerto: Animação rápida de sucesso, som de acerto, aumento da pontuação.
Erro/Tempo Esgotado: Animação de erro, som de erro, fim de jogo.
Pontuação: Acompanha a pontuação atual do jogador em uma rodada.
Sequência de Acertos (Streak): Acompanha a sequência de acertos consecutivos.
2.3. Tela de Fim de Jogo (Game Over)
Pontuação Final: Exibe a pontuação obtida na rodada.
Maior Pontuação: Mostra a maior pontuação já alcançada pelo jogador.
Botão "Jogar Novamente": Permite iniciar uma nova rodada.
Botão "Menu Principal": Retorna à tela de início.
Opção de Anúncio Recompensado: Oferecer um botão "Continuar" ou "Ganhar mais tempo" que, ao ser clicado, exibe um anúncio em vídeo recompensado. Após a conclusão do anúncio, o jogador retorna à rodada (ex: com mais tempo ou uma vida extra).
2.4. Conteúdo (Fatos)
Banco de Dados Local: Implementação inicial com fatos armazenados localmente no aplicativo (arquivos JSON ou listas Dart).
Quantidade de Fatos: Mínimo de 50 a 100 fatos curtos e interessantes, com suas respectivas categorias de julgamento e a resposta correta para cada categoria.
Formato do Fato: Deve conter, no mínimo, id, texto_do_fato, categoria_1, categoria_2 (ou mais, dependendo do tipo de julgamento), resposta_correta (qual categoria é a certa).
2.5. Integração de Anúncios (Google AdMob)
Anúncios Intersticiais: Exibidos em pontos estratégicos (ex: a cada 3 "game overs" ou após uma série de rodadas).
Anúncios em Vídeo Recompensados: Integrados na tela de fim de jogo para oferecer uma vantagem ao jogador em troca da visualização.
Uso de IDs de Teste: O desenvolvimento deve ser feito com IDs de anúncios de teste do AdMob. Os IDs de produção devem ser configurados apenas antes do lançamento.
3. Requisitos Técnicos
Framework: Flutter
Motor de Jogo: Flame Engine (versão estável mais recente)
Linguagem de Programação: Dart
Gerenciamento de Estado: Abordagem simples e eficaz para o estado do jogo (ex: GameComponents, ValueNotifiers).
Assets: Ícones, fundos, elementos de UI e efeitos sonoros básicos e licenciados para uso comercial.
4. Requisitos de Qualidade e Desempenho
Fluidez: O jogo deve rodar a 60 FPS (quadros por segundo) na maioria dos dispositivos modernos.
Responsividade: A UI e as mecânicas de toque devem ser responsivas e adaptáveis a diferentes tamanhos de tela (smartphones).
Estabilidade: O aplicativo não deve apresentar crashes frequentes.
Experiência do Usuário (UX): A mecânica de "fling" deve ser intuitiva e satisfatória. Os menus devem ser fáceis de navegar.
Otimização: O tamanho do APK/IPA deve ser minimizado.
5. Processo de Desenvolvimento e Entrega
Controle de Versão: Usar Git (ex: GitHub, GitLab) para controle de versão do código.
Testes: Realizar testes de funcionalidade e usabilidade em dispositivos Android e iOS.
Documentação: Código limpo e comentado. Breve documentação sobre como adicionar novos fatos.
Entrega:
Código-fonte completo.
Builds de teste para Android (APK/AAB) e iOS (IPA).
Instruções detalhadas para compilar e executar o projeto.
Artefatos de design (ícones, screenshots para a loja).
6. Próximos Passos (Pós-MVP)
Mais Conteúdo: Adicionar centenas, talvez milhares de novos fatos. Considerar a migração para um banco de dados em nuvem (Firebase Firestore/Supabase) para atualizações dinâmicas.
Líderes Online: Implementar placares de líderes globais (usando Firebase ou Play Games Services/Game Center).
Personalização: Desbloqueio de temas ou estilos visuais.
Novos Modos de Jogo: Modos baseados em tempo, precisão, ou categorias específicas.
Experiência de Anúncios Premium: Opção de compra in-app para remover anúncios.
Esta especificação serve como um guia claro para o desenvolvimento do "Daily Fact Fling", focando 