<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android">
  <com.google.android.gms.ads.nativead.NativeAdView
      android:layout_height="match_parent"
      android:layout_width="match_parent"
      android:layout_centerInParent="true"
      android:id="@+id/native_ad_view"
      >

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_height="wrap_content"
        android:layout_width="match_parent"
        android:layout_centerInParent="true"
        android:background="@drawable/gnt_outline_shape"
        >
      <androidx.constraintlayout.widget.ConstraintLayout
          android:layout_width="@dimen/gnt_no_size"
          android:layout_height="@dimen/gnt_no_size"
          android:layout_marginEnd="@dimen/gnt_default_margin"
          android:layout_marginStart="@dimen/gnt_default_margin"
          android:layout_marginTop="@dimen/gnt_default_margin"
          android:layout_marginBottom="@dimen/gnt_default_margin"
          app:layout_constraintDimensionRatio="H,4:1"
          app:layout_constraintEnd_toEndOf="parent"
          app:layout_constraintStart_toStartOf="parent"
          app:layout_constraintTop_toTopOf="parent"
          app:layout_constraintBottom_toBottomOf="parent"
          android:orientation="horizontal"
          android:id="@+id/background"
          >
        <ImageView
            android:id="@+id/icon"
            android:layout_width="0dp"
            android:layout_weight="0"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/gnt_no_margin"
            android:layout_marginStart="@dimen/gnt_no_margin"
            android:layout_marginBottom="@dimen/gnt_no_margin"
            android:layout_marginEnd="@dimen/gnt_no_margin"
            app:layout_constraintDimensionRatio="H,1:1"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/content"
            />
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="@dimen/gnt_no_size"
            android:layout_height="@dimen/gnt_no_size"
            android:layout_marginTop="@dimen/gnt_no_margin"
            android:layout_marginBottom="@dimen/gnt_no_margin"
            android:layout_marginStart="@dimen/gnt_default_margin"
            android:layout_marginEnd="@dimen/gnt_default_margin"
            android:orientation="vertical"
            android:id="@+id/content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/icon"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            >
          <LinearLayout
              android:id="@+id/headline"
              android:orientation="horizontal"
              android:layout_width="match_parent"
              android:layout_height="@dimen/gnt_no_size"
              android:layout_weight="@dimen/gnt_text_row_weight"
              app:layout_constraintBottom_toTopOf="@+id/row_two"
              app:layout_constraintEnd_toEndOf="parent"
              app:layout_constraintStart_toStartOf="parent"
              app:layout_constraintTop_toTopOf="parent"
              android:background="@android:color/transparent"
              >

            <TextView
                android:id="@+id/primary"
                android:textStyle="bold"
                android:textSize="@dimen/gnt_text_size_large"
                android:textColor="@color/gnt_gray"
                android:lines="1"
                android:layout_height="match_parent"
                android:layout_width="wrap_content"
                android:layout_marginTop="@dimen/gnt_no_margin"
                android:layout_marginBottom="@dimen/gnt_no_margin"
                android:layout_marginStart="@dimen/gnt_no_margin"
                android:layout_marginEnd="@dimen/gnt_no_margin"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                ></TextView>
          </LinearLayout>
          <LinearLayout
              android:id="@+id/row_two"
              android:orientation="horizontal"
              android:layout_width="match_parent"
              android:layout_height="@dimen/gnt_no_size"
              android:layout_weight="@dimen/gnt_text_row_weight"
              app:layout_constraintBottom_toTopOf="@+id/cta"
              app:layout_constraintEnd_toEndOf="parent"
              app:layout_constraintStart_toStartOf="parent"
              app:layout_constraintTop_toBottomOf="@id/headline"
              >
            <TextView
                android:background="@drawable/gnt_rounded_corners_shape"
                android:layout_width="@dimen/gnt_ad_indicator_width"
                android:gravity="center"
                android:id="@+id/ad_notification_view"
                android:layout_height="@dimen/gnt_ad_indicator_height"
                android:layout_marginTop="@dimen/gnt_ad_indicator_top_margin"
                android:layout_marginStart="@dimen/gnt_no_margin"
                android:layout_marginEnd="@dimen/gnt_default_margin"
                android:text="Ad"
                android:textColor="@color/gnt_ad_green"
                android:textStyle="bold"
                android:textSize="@dimen/gnt_ad_indicator_text_size"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                >
            </TextView>
            <RatingBar
                android:id="@+id/rating_bar"
                android:background="@android:color/transparent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/gnt_text_size_small"
                android:textColor="@color/gnt_gray"
                android:numStars="5"
                android:stepSize="0.1"
                android:lines="1"
                android:layout_marginTop="@dimen/gnt_no_margin"
                android:layout_marginBottom="@dimen/gnt_no_margin"
                android:layout_marginStart="@dimen/gnt_no_margin"
                android:layout_marginEnd="@dimen/gnt_no_margin"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/ad_notification_view"
                style="?android:attr/ratingBarStyleSmall"
                app:layout_constraintTop_toTopOf="parent">

            </RatingBar>
            <TextView
                android:id="@+id/secondary"
                android:background="@color/gnt_white"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="top"
                android:textSize="@dimen/gnt_text_size_small"
                android:textColor="@color/gnt_gray"
                android:lines="1"
                android:layout_marginTop="@dimen/gnt_no_margin"
                android:layout_marginBottom="@dimen/gnt_no_margin"
                android:layout_marginStart="@dimen/gnt_no_margin"
                android:layout_marginEnd="@dimen/gnt_no_margin"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/ad_notification_view"
                app:layout_constraintTop_toTopOf="parent"
                ></TextView>

          </LinearLayout>
          <androidx.appcompat.widget.AppCompatButton
              android:id="@+id/cta"
              android:layout_width="match_parent"
              android:layout_height="@dimen/gnt_no_size"
              android:background="@color/gnt_blue"
              android:textColor="@color/gnt_white"
              android:lines="1"
              app:layout_constraintBottom_toBottomOf="parent"
              app:layout_constraintEnd_toEndOf="parent"
              app:layout_constraintStart_toStartOf="parent"
              app:layout_constraintTop_toBottomOf="@id/row_two"
              android:gravity="center"
              app:layout_constraintHeight_percent="0.35"
              />


        </androidx.constraintlayout.widget.ConstraintLayout>


      </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

  </com.google.android.gms.ads.nativead.NativeAdView>

</merge>
