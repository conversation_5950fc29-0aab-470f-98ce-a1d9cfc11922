{"buildFiles": ["F:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["F:\\Dev\\Android_SDK_HOME\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\.cxx\\Debug\\8z1f4jx5\\x86", "clean"]], "buildTargetsCommandComponents": ["F:\\Dev\\Android_SDK_HOME\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\.cxx\\Debug\\8z1f4jx5\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}