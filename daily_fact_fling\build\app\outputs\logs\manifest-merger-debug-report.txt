-- Merging decision tree log ---
application
INJECTED from F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\android\app\src\main\AndroidManifest.xml:2:5-33:19
INJECTED from F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\android\app\src\debug\AndroidManifest.xml
MERGED from [:google_mobile_ads] F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\build\google_mobile_ads\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-13:19
MERGED from [:google_mobile_ads] F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\build\google_mobile_ads\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-13:19
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:70:5-110:19
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:70:5-110:19
MERGED from [com.google.android.gms:play-services-ads-base:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\992c08c7f2820b659c2c69596e8b4628\transformed\jetified-play-services-ads-base-23.6.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-ads-base:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\992c08c7f2820b659c2c69596e8b4628\transformed\jetified-play-services-ads-base-23.6.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\19e5c96d7b98bac1a08d2c99339ede92\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\19e5c96d7b98bac1a08d2c99339ede92\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a02c4e47ef8b771b0b6fe82402bd1dbd\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a02c4e47ef8b771b0b6fe82402bd1dbd\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e20001cc41f5487e719977c4b5116946\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e20001cc41f5487e719977c4b5116946\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7795f05fbf8283409257f8d4d50c7a58\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7795f05fbf8283409257f8d4d50c7a58\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\b5fd6c0ed2eedb4736347c922917a367\transformed\jetified-play-services-measurement-base-20.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\b5fd6c0ed2eedb4736347c922917a367\transformed\jetified-play-services-measurement-base-20.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1186d146da5ef23629d7bf94e5a0d382\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1186d146da5ef23629d7bf94e5a0d382\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\f43a8a95ab2f768e2215dacb56c18080\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\f43a8a95ab2f768e2215dacb56c18080\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed9bdd709f3d824b8d47f92637346759\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed9bdd709f3d824b8d47f92637346759\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\93ae00d92d58cf4d438d862099c7d5b1\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\93ae00d92d58cf4d438d862099c7d5b1\transformed\room-runtime-2.2.5\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f3b8632830106ae874bd20aa567485d8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f3b8632830106ae874bd20aa567485d8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:name
		INJECTED from F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\android\app\src\main\AndroidManifest.xml
manifest
ADDED from F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\android\app\src\main\AndroidManifest.xml:1:1-45:12
MERGED from F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\android\app\src\main\AndroidManifest.xml:1:1-45:12
INJECTED from F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:shared_preferences_android] F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:google_mobile_ads] F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\build\google_mobile_ads\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:webview_flutter_android] F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\build\webview_flutter_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:audioplayers_android] F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\build\audioplayers_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:path_provider_android] F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-ads:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e5aa58ad971ef0b034990717d52ef9e6\transformed\jetified-play-services-ads-23.6.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:17:1-112:12
MERGED from [com.google.android.ump:user-messaging-platform:3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7bbc2026af90f1780d056fbd2e9023dd\transformed\jetified-user-messaging-platform-3.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-ads-base:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\992c08c7f2820b659c2c69596e8b4628\transformed\jetified-play-services-ads-base-23.6.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\19e5c96d7b98bac1a08d2c99339ede92\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a02c4e47ef8b771b0b6fe82402bd1dbd\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e20001cc41f5487e719977c4b5116946\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7795f05fbf8283409257f8d4d50c7a58\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\933f7f3fb7967ea3aa8d8dddc60e1050\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\b5fd6c0ed2eedb4736347c922917a367\transformed\jetified-play-services-measurement-base-20.1.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1186d146da5ef23629d7bf94e5a0d382\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\f43a8a95ab2f768e2215dacb56c18080\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b83b8b00b8346c9e7414a1f1298f055d\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a295c1332cd792405fffabf7b4bbac54\transformed\appcompat-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\972419750b36e9fbf2d0c26a45927d82\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c8746a36ac065afed39d95b2852a559\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6d1284f70efaea5e2f7428ac2d9ae231\transformed\webkit-1.12.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\79275990ee9dddfd68bc7c9d7157e0cd\transformed\recyclerview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\984bdeb02044daf662dc2d3e1fe07483\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f71e40716bc29995f4cada24da499d83\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\51d67b28f04358995f888f3317b40779\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c69679757972620720ec039d7103818\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\84a8a15495959e34577fc85968c66982\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e60d7cc8f585e105683d15c0883739b4\transformed\jetified-activity-1.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d21df4d1a80ec9bf2502ed8e05d37297\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\df95e149204d983cb4c11efbc84ab4c1\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d2f89760d0d7afc020c36efe677962c0\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\49e80b404c42bd03696302491626128a\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed9bdd709f3d824b8d47f92637346759\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c244a6ce50b3288fe79d3f6ae212397f\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d813ca98b23cd30a6261e035c9b7933\transformed\jetified-lifecycle-service-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e72f610bb8a20735f78a04c908b9b793\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc0590902d0fbba9efca7bc74a8bc4cb\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\306016bcb4195b3238dbb4d76cafb64c\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\84addddb59162e1cea52976d5f2c6cc1\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1e1e86d9fc1ea8180a98a95859125403\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\23f1459f3a17c3f297faa9e854d895db\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7b4e62af0008ea11d5619489212cc48\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\185f2479ab24942c0bba65b9ff947d79\transformed\jetified-appcompat-resources-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\10acfa95459151f0abcb0437238b9ca7\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\28f988f0d4c2cc22199e4c3cefdd595e\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\08d6944c906bcd30c9d42a63993176cf\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f87704cc6ac259b753f491455f413615\transformed\transition-1.4.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\afec9dc0bcc11d087323dc11f5e0350a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ee817ed912dd87a9ffe7b0d8087b9e11\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ccda8ddd57f5a835df89427c6970b69a\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\106b34a8e64882148068274b889c0b9f\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa55b2079cbc673a6a445c1850daa153\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\425b3275685a974b685af27ff4ed6b1d\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7f734b899c9b5bcf473e5c8a79b68b93\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\27003765dae66b7dc3bf878451ba1684\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\31eccd218f5b1fd8272959453f411784\transformed\jetified-datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\7535a935f9e65beb6c79d36312378a64\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\479b3bf32f00901a230d7d79262001b9\transformed\jetified-datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\93ae00d92d58cf4d438d862099c7d5b1\transformed\room-runtime-2.2.5\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1d33f966f1aab687e952d4b7cce6845e\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f3b8632830106ae874bd20aa567485d8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc56adcbf17b642cc8bc810bfcbda96d\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cfa7aabd0ff8beb21daa4d12f46b519\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\449958d8d573c37840f9e10ca78b3740\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\124c722ec60eb5d4bed38309f513015b\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e11ad8278833075c06aac72b9b20fe77\transformed\sqlite-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a58c138301656e62a00a9163f21e3a54\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cb263d6c3807b4994a64a61fe4ea2bcf\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7f63da0fad92ba134922d35b82d48c3\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\683cbfde6a58705556f8fa87883a18e1\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa2b156f95f9eab66ccb02ea9eacfedd\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\android\app\src\main\AndroidManifest.xml:1:11-69
queries
ADDED from F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\android\app\src\main\AndroidManifest.xml:39:5-44:15
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:35:5-68:15
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:35:5-68:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\android\app\src\main\AndroidManifest.xml:40:9-43:18
action#android.intent.action.PROCESS_TEXT
ADDED from F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\android\app\src\main\AndroidManifest.xml:41:13-72
	android:name
		ADDED from F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\android\app\src\main\AndroidManifest.xml:41:21-70
data
ADDED from F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\android\app\src\main\AndroidManifest.xml:42:13-50
	android:mimeType
		ADDED from F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\android\app\src\main\AndroidManifest.xml:42:19-48
uses-permission#android.permission.INTERNET
ADDED from F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\android\app\src\debug\AndroidManifest.xml:6:5-66
MERGED from [:google_mobile_ads] F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\build\google_mobile_ads\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [:google_mobile_ads] F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\build\google_mobile_ads\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\933f7f3fb7967ea3aa8d8dddc60e1050\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\933f7f3fb7967ea3aa8d8dddc60e1050\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:23:5-67
	android:name
		ADDED from F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\android\app\src\debug\AndroidManifest.xml:6:22-64
uses-sdk
INJECTED from F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\android\app\src\debug\AndroidManifest.xml
INJECTED from F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\android\app\src\debug\AndroidManifest.xml
MERGED from [:shared_preferences_android] F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\build\shared_preferences_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_mobile_ads] F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\build\google_mobile_ads\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:google_mobile_ads] F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\build\google_mobile_ads\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:webview_flutter_android] F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\build\webview_flutter_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:webview_flutter_android] F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\build\webview_flutter_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:audioplayers_android] F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\build\audioplayers_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:audioplayers_android] F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\build\audioplayers_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\build\path_provider_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e5aa58ad971ef0b034990717d52ef9e6\transformed\jetified-play-services-ads-23.6.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-ads:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e5aa58ad971ef0b034990717d52ef9e6\transformed\jetified-play-services-ads-23.6.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7bbc2026af90f1780d056fbd2e9023dd\transformed\jetified-user-messaging-platform-3.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7bbc2026af90f1780d056fbd2e9023dd\transformed\jetified-user-messaging-platform-3.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads-base:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\992c08c7f2820b659c2c69596e8b4628\transformed\jetified-play-services-ads-base-23.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads-base:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\992c08c7f2820b659c2c69596e8b4628\transformed\jetified-play-services-ads-base-23.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\19e5c96d7b98bac1a08d2c99339ede92\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\19e5c96d7b98bac1a08d2c99339ede92\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a02c4e47ef8b771b0b6fe82402bd1dbd\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a02c4e47ef8b771b0b6fe82402bd1dbd\transformed\jetified-play-services-appset-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e20001cc41f5487e719977c4b5116946\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e20001cc41f5487e719977c4b5116946\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7795f05fbf8283409257f8d4d50c7a58\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7795f05fbf8283409257f8d4d50c7a58\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\933f7f3fb7967ea3aa8d8dddc60e1050\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\933f7f3fb7967ea3aa8d8dddc60e1050\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\b5fd6c0ed2eedb4736347c922917a367\transformed\jetified-play-services-measurement-base-20.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\b5fd6c0ed2eedb4736347c922917a367\transformed\jetified-play-services-measurement-base-20.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1186d146da5ef23629d7bf94e5a0d382\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1186d146da5ef23629d7bf94e5a0d382\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\f43a8a95ab2f768e2215dacb56c18080\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\f43a8a95ab2f768e2215dacb56c18080\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b83b8b00b8346c9e7414a1f1298f055d\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b83b8b00b8346c9e7414a1f1298f055d\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a295c1332cd792405fffabf7b4bbac54\transformed\appcompat-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a295c1332cd792405fffabf7b4bbac54\transformed\appcompat-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\972419750b36e9fbf2d0c26a45927d82\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\972419750b36e9fbf2d0c26a45927d82\transformed\jetified-fragment-ktx-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c8746a36ac065afed39d95b2852a559\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1c8746a36ac065afed39d95b2852a559\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6d1284f70efaea5e2f7428ac2d9ae231\transformed\webkit-1.12.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\6d1284f70efaea5e2f7428ac2d9ae231\transformed\webkit-1.12.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\79275990ee9dddfd68bc7c9d7157e0cd\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\79275990ee9dddfd68bc7c9d7157e0cd\transformed\recyclerview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\984bdeb02044daf662dc2d3e1fe07483\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\984bdeb02044daf662dc2d3e1fe07483\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f71e40716bc29995f4cada24da499d83\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f71e40716bc29995f4cada24da499d83\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\51d67b28f04358995f888f3317b40779\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\51d67b28f04358995f888f3317b40779\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c69679757972620720ec039d7103818\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c69679757972620720ec039d7103818\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\84a8a15495959e34577fc85968c66982\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\84a8a15495959e34577fc85968c66982\transformed\jetified-activity-ktx-1.8.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e60d7cc8f585e105683d15c0883739b4\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e60d7cc8f585e105683d15c0883739b4\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d21df4d1a80ec9bf2502ed8e05d37297\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d21df4d1a80ec9bf2502ed8e05d37297\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\df95e149204d983cb4c11efbc84ab4c1\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\df95e149204d983cb4c11efbc84ab4c1\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d2f89760d0d7afc020c36efe677962c0\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\d2f89760d0d7afc020c36efe677962c0\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\49e80b404c42bd03696302491626128a\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\49e80b404c42bd03696302491626128a\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed9bdd709f3d824b8d47f92637346759\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed9bdd709f3d824b8d47f92637346759\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c244a6ce50b3288fe79d3f6ae212397f\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c244a6ce50b3288fe79d3f6ae212397f\transformed\jetified-lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d813ca98b23cd30a6261e035c9b7933\transformed\jetified-lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7d813ca98b23cd30a6261e035c9b7933\transformed\jetified-lifecycle-service-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e72f610bb8a20735f78a04c908b9b793\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e72f610bb8a20735f78a04c908b9b793\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc0590902d0fbba9efca7bc74a8bc4cb\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc0590902d0fbba9efca7bc74a8bc4cb\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\306016bcb4195b3238dbb4d76cafb64c\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\306016bcb4195b3238dbb4d76cafb64c\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\84addddb59162e1cea52976d5f2c6cc1\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\84addddb59162e1cea52976d5f2c6cc1\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1e1e86d9fc1ea8180a98a95859125403\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1e1e86d9fc1ea8180a98a95859125403\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\23f1459f3a17c3f297faa9e854d895db\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\23f1459f3a17c3f297faa9e854d895db\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7b4e62af0008ea11d5619489212cc48\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\e7b4e62af0008ea11d5619489212cc48\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\185f2479ab24942c0bba65b9ff947d79\transformed\jetified-appcompat-resources-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\185f2479ab24942c0bba65b9ff947d79\transformed\jetified-appcompat-resources-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\10acfa95459151f0abcb0437238b9ca7\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\10acfa95459151f0abcb0437238b9ca7\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\28f988f0d4c2cc22199e4c3cefdd595e\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\28f988f0d4c2cc22199e4c3cefdd595e\transformed\coordinatorlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\08d6944c906bcd30c9d42a63993176cf\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\08d6944c906bcd30c9d42a63993176cf\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f87704cc6ac259b753f491455f413615\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f87704cc6ac259b753f491455f413615\transformed\transition-1.4.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\afec9dc0bcc11d087323dc11f5e0350a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\afec9dc0bcc11d087323dc11f5e0350a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ee817ed912dd87a9ffe7b0d8087b9e11\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ee817ed912dd87a9ffe7b0d8087b9e11\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ccda8ddd57f5a835df89427c6970b69a\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\ccda8ddd57f5a835df89427c6970b69a\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\106b34a8e64882148068274b889c0b9f\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\106b34a8e64882148068274b889c0b9f\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa55b2079cbc673a6a445c1850daa153\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa55b2079cbc673a6a445c1850daa153\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\425b3275685a974b685af27ff4ed6b1d\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\425b3275685a974b685af27ff4ed6b1d\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7f734b899c9b5bcf473e5c8a79b68b93\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7f734b899c9b5bcf473e5c8a79b68b93\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\27003765dae66b7dc3bf878451ba1684\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\27003765dae66b7dc3bf878451ba1684\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\31eccd218f5b1fd8272959453f411784\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\31eccd218f5b1fd8272959453f411784\transformed\jetified-datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\7535a935f9e65beb6c79d36312378a64\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\7535a935f9e65beb6c79d36312378a64\transformed\jetified-datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\479b3bf32f00901a230d7d79262001b9\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\479b3bf32f00901a230d7d79262001b9\transformed\jetified-datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\93ae00d92d58cf4d438d862099c7d5b1\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\93ae00d92d58cf4d438d862099c7d5b1\transformed\room-runtime-2.2.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1d33f966f1aab687e952d4b7cce6845e\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1d33f966f1aab687e952d4b7cce6845e\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f3b8632830106ae874bd20aa567485d8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\f3b8632830106ae874bd20aa567485d8\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc56adcbf17b642cc8bc810bfcbda96d\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\dc56adcbf17b642cc8bc810bfcbda96d\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cfa7aabd0ff8beb21daa4d12f46b519\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4cfa7aabd0ff8beb21daa4d12f46b519\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\449958d8d573c37840f9e10ca78b3740\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\449958d8d573c37840f9e10ca78b3740\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\124c722ec60eb5d4bed38309f513015b\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\124c722ec60eb5d4bed38309f513015b\transformed\sqlite-framework-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e11ad8278833075c06aac72b9b20fe77\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e11ad8278833075c06aac72b9b20fe77\transformed\sqlite-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a58c138301656e62a00a9163f21e3a54\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a58c138301656e62a00a9163f21e3a54\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cb263d6c3807b4994a64a61fe4ea2bcf\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cb263d6c3807b4994a64a61fe4ea2bcf\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7f63da0fad92ba134922d35b82d48c3\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\b7f63da0fad92ba134922d35b82d48c3\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\683cbfde6a58705556f8fa87883a18e1\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\683cbfde6a58705556f8fa87883a18e1\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa2b156f95f9eab66ccb02ea9eacfedd\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\aa2b156f95f9eab66ccb02ea9eacfedd\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
	tools:overrideLibrary
		ADDED from [com.google.android.gms:play-services-ads:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e5aa58ad971ef0b034990717d52ef9e6\transformed\jetified-play-services-ads-23.6.0\AndroidManifest.xml:23:9-49
	android:targetSdkVersion
		INJECTED from F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\android\app\src\debug\AndroidManifest.xml
meta-data#io.flutter.embedded_views_preview
ADDED from [:google_mobile_ads] F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\build\google_mobile_ads\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:9-12:36
	android:value
		ADDED from [:google_mobile_ads] F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\build\google_mobile_ads\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-33
	android:name
		ADDED from [:google_mobile_ads] F:\Projetos\02 - Dev\daylyFacts\daily_fact_fling\build\google_mobile_ads\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-61
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\933f7f3fb7967ea3aa8d8dddc60e1050\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\933f7f3fb7967ea3aa8d8dddc60e1050\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:26:5-79
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:25:22-76
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\19e5c96d7b98bac1a08d2c99339ede92\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\19e5c96d7b98bac1a08d2c99339ede92\transformed\jetified-play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\933f7f3fb7967ea3aa8d8dddc60e1050\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\933f7f3fb7967ea3aa8d8dddc60e1050\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:26:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:27:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:27:22-79
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:28:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:28:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_TOPICS
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:29:5-83
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:29:22-80
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:30:5-32:31
REJECTED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:27:5-81
	tools:node
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:32:9-28
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:31:9-65
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:38:9-44:18
action#android.intent.action.VIEW
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:39:13-65
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:39:21-62
category#android.intent.category.BROWSABLE
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:41:13-74
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:41:23-71
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:47:9-49:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:48:13-90
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:48:21-87
intent#action:name:android.intent.action.INSERT+data:mimeType:vnd.android.cursor.dir/event
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:52:9-56:18
action#android.intent.action.INSERT
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:53:13-67
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:53:21-64
intent#action:name:android.intent.action.VIEW+data:scheme:sms
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:57:9-61:18
intent#action:name:android.intent.action.DIAL+data:path:tel:
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:62:9-66:18
action#android.intent.action.DIAL
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:63:13-65
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:63:21-62
activity#com.google.android.gms.ads.AdActivity
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:73:9-78:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:76:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:78:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:75:13-122
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:77:13-61
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:74:13-65
provider#com.google.android.gms.ads.MobileAdsInitProvider
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:80:9-85:43
	android:authorities
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:82:13-73
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:83:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:85:13-40
	android:initOrder
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:84:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:81:13-76
service#com.google.android.gms.ads.AdService
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:87:9-91:43
	android:enabled
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:89:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:90:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:91:13-40
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:88:13-64
activity#com.google.android.gms.ads.OutOfContextTestingActivity
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:93:9-97:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:96:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:97:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:95:13-122
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:94:13-82
activity#com.google.android.gms.ads.NotificationHandlerActivity
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:98:9-105:43
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:100:13-46
	android:launchMode
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:102:13-44
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:101:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:105:13-40
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:104:13-72
	android:taskAffinity
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:103:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:99:13-82
property#android.adservices.AD_SERVICES_CONFIG
ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:107:9-109:62
	android:resource
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:109:13-59
	android:name
		ADDED from [com.google.android.gms:play-services-ads-lite:23.6.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\fc83ed23dd70c2212cc258f35335ac1d\transformed\jetified-play-services-ads-lite-23.6.0\AndroidManifest.xml:108:13-65
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e20001cc41f5487e719977c4b5116946\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e20001cc41f5487e719977c4b5116946\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e20001cc41f5487e719977c4b5116946\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\e20001cc41f5487e719977c4b5116946\transformed\jetified-play-services-base-18.0.0\AndroidManifest.xml:20:19-85
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\933f7f3fb7967ea3aa8d8dddc60e1050\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:25:5-68
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\933f7f3fb7967ea3aa8d8dddc60e1050\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:22-65
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1186d146da5ef23629d7bf94e5a0d382\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1186d146da5ef23629d7bf94e5a0d382\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\1186d146da5ef23629d7bf94e5a0d382\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed9bdd709f3d824b8d47f92637346759\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed9bdd709f3d824b8d47f92637346759\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed9bdd709f3d824b8d47f92637346759\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:28:22-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85879f220671a879b538e8ef16ed1744\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:35:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:33:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:32:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:36:13-38:52
	android:value
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:38:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:37:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:41:9-46:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:44:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:45:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:46:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:43:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:42:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:47:9-53:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:50:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:51:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:52:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:53:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:49:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:48:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:54:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:57:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:58:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:56:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:55:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\8f2d61afc8b21a772bd0b7bc042cb3cc\transformed\work-runtime-2.7.0\AndroidManifest.xml:140:25-85
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.example.dailyfactfling.daily_fact_fling.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.example.dailyfactfling.daily_fact_fling.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\93ae00d92d58cf4d438d862099c7d5b1\transformed\room-runtime-2.2.5\AndroidManifest.xml:25:9-28:40
	android:exported
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\93ae00d92d58cf4d438d862099c7d5b1\transformed\room-runtime-2.2.5\AndroidManifest.xml:28:13-37
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\93ae00d92d58cf4d438d862099c7d5b1\transformed\room-runtime-2.2.5\AndroidManifest.xml:27:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.2.5] C:\Users\<USER>\.gradle\caches\8.12\transforms\93ae00d92d58cf4d438d862099c7d5b1\transformed\room-runtime-2.2.5\AndroidManifest.xml:26:13-74
