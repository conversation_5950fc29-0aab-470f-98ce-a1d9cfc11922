{"logs": [{"outputFile": "com.example.dailyfactfling.daily_fact_fling.app-mergeDebugResources-50:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7f734b899c9b5bcf473e5c8a79b68b93\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "752", "startColumns": "4", "startOffsets": "39074", "endColumns": "53", "endOffsets": "39123"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\197f12b192a3f06912c946d4cbd2dd7d\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "10,67,73,81,339,351,357,363,364,365,366,367,705,2658,2664,5011,5019,5034", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "445,2984,3157,3376,14746,15060,15248,15435,15488,15548,15600,15645,36693,163814,164009,250879,251161,251775", "endLines": "10,72,80,88,350,356,362,363,364,365,366,367,705,2663,2668,5018,5033,5049", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "499,3152,3371,3590,15055,15243,15430,15483,15543,15595,15640,15679,36748,164004,164162,251156,251770,252424"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\28f988f0d4c2cc22199e4c3cefdd595e\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "39,2551,3883,3889", "startColumns": "4,4,4,4", "startOffsets": "1818,160146,208723,208934", "endLines": "39,2553,3888,3972", "endColumns": "60,12,24,24", "endOffsets": "1874,160286,208929,213445"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0c69679757972620720ec039d7103818\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "445,446,447,448,594,595,817,819,820,821", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "19701,19759,19825,19888,29618,29689,44580,44705,44772,44851", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "19754,19820,19883,19945,29684,29756,44643,44767,44846,44915"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a295c1332cd792405fffabf7b4bbac54\\transformed\\appcompat-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,219,220,224,228,232,237,243,250,254,258,263,267,271,275,279,283,287,293,297,303,307,313,317,322,326,329,333,339,343,349,353,359,362,366,370,374,378,382,383,384,385,388,391,394,397,401,402,403,404,405,408,410,412,414,419,420,424,430,434,435,437,448,449,453,459,463,464,465,469,496,500,501,505,533,703,729,899,925,956,964,970,984,1006,1011,1016,1026,1035,1044,1048,1055,1063,1070,1071,1080,1083,1086,1090,1094,1098,1101,1102,1107,1112,1122,1127,1134,1140,1141,1144,1148,1153,1155,1157,1160,1163,1165,1169,1172,1179,1182,1185,1189,1191,1195,1197,1199,1201,1205,1213,1221,1233,1239,1248,1251,1262,1265,1266,1271,1272,1277,1346,1416,1417,1427,1436,1437,1439,1443,1446,1449,1452,1455,1458,1461,1464,1468,1471,1474,1477,1481,1484,1488,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1514,1516,1517,1518,1519,1520,1521,1522,1523,1525,1526,1528,1529,1531,1533,1534,1536,1537,1538,1539,1540,1541,1543,1544,1545,1546,1547,1548,1550,1552,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1568,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,535,605,666,741,817,894,972,1057,1139,1215,1291,1368,1446,1552,1658,1737,1817,1874,1932,2006,2081,2146,2212,2272,2333,2405,2478,2545,2613,2672,2731,2790,2849,2908,2962,3016,3069,3123,3177,3231,3285,3359,3438,3511,3585,3656,3728,3800,3873,3930,3988,4061,4135,4209,4284,4356,4429,4499,4570,4630,4691,4760,4829,4899,4973,5049,5113,5190,5266,5343,5408,5477,5554,5629,5698,5766,5843,5909,5970,6067,6132,6201,6300,6371,6430,6488,6545,6604,6668,6739,6811,6883,6955,7027,7094,7162,7230,7289,7352,7416,7506,7597,7657,7723,7790,7856,7926,7990,8043,8110,8171,8238,8351,8409,8472,8537,8602,8677,8750,8822,8871,8932,8993,9054,9116,9180,9244,9308,9373,9436,9496,9557,9623,9682,9742,9804,9875,9935,10003,10089,10176,10266,10353,10441,10523,10606,10696,10787,10839,10897,10942,11008,11072,11129,11186,11240,11297,11345,11394,11445,11479,11526,11575,11621,11653,11717,11779,11839,11896,11970,12040,12118,12172,12242,12327,12375,12421,12482,12545,12611,12675,12746,12809,12874,12938,12999,13060,13112,13185,13259,13328,13403,13477,13551,13692,13762,13815,13893,13983,14071,14167,14257,14839,14928,15175,15456,15708,15993,16386,16863,17085,17307,17583,17810,18040,18270,18500,18730,18957,19376,19602,20027,20257,20685,20904,21187,21395,21526,21753,22179,22404,22831,23052,23477,23597,23873,24174,24498,24789,25103,25240,25371,25476,25718,25885,26089,26297,26568,26680,26792,26897,27014,27228,27374,27514,27600,27948,28036,28282,28700,28949,29031,29129,29746,29846,30098,30522,30777,30871,30960,31197,33249,33491,33593,33846,36030,47063,48579,59710,61238,62995,63621,64041,65102,66367,66623,66859,67406,67900,68505,68703,69283,69847,70222,70340,70878,71035,71231,71504,71760,71930,72071,72135,72500,72867,73543,73807,74145,74498,74592,74778,75084,75346,75471,75598,75837,76048,76167,76360,76537,76992,77173,77295,77554,77667,77854,77956,78063,78192,78467,78975,79471,80348,80642,81212,81361,82093,82265,82349,82685,82777,83055,88464,94016,94078,94708,95322,95413,95526,95755,95915,96067,96238,96404,96573,96740,96903,97146,97316,97489,97660,97934,98133,98338,98668,98752,98848,98944,99042,99142,99244,99346,99448,99550,99652,99752,99848,99960,100089,100212,100343,100474,100572,100686,100780,100920,101054,101150,101262,101362,101478,101574,101686,101786,101926,102062,102226,102356,102514,102664,102805,102949,103084,103196,103346,103474,103602,103738,103870,104000,104130,104242,104382,104528,104672,104810,104876,104966,105042,105146,105236,105338,105446,105554,105654,105734,105826,105924,106034,106086,106164,106270,106362,106466,106576,106698,106861,107018,107098,107198,107288,107398,107488,107729,107823,107929,108021,108121,108233,108347,108463,108579,108673,108787,108899,109001,109121,109243,109325,109429,109549,109675,109773,109867,109955,110067,110183,110305,110417,110592,110708,110794,110886,110998,111122,111189,111315,111383,111511,111655,111783,111852,111947,112062,112175,112274,112383,112494,112605,112706,112811,112911,113041,113132,113255,113349,113461,113547,113651,113747,113835,113953,114057,114161,114287,114375,114483,114583,114673,114783,114867,114969,115053,115107,115171,115277,115363,115473,115557,115677,120821,120939,121054,121186,121901,122593,123110,124709,126242,126630,131365,151627,151887,153397,154430,156443,156705,157061,157891,164673,165807,166101,166324,166651,168701,169349,173200,174402,178481,179696,181105", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,702,728,898,924,955,963,969,983,1005,1010,1015,1025,1034,1043,1047,1054,1062,1069,1070,1079,1082,1085,1089,1093,1097,1100,1101,1106,1111,1121,1126,1133,1139,1140,1143,1147,1152,1154,1156,1159,1162,1164,1168,1171,1178,1181,1184,1188,1190,1194,1196,1198,1200,1204,1212,1220,1232,1238,1247,1250,1261,1264,1265,1270,1271,1276,1345,1415,1416,1426,1435,1436,1438,1442,1445,1448,1451,1454,1457,1460,1463,1467,1470,1473,1476,1480,1483,1487,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1513,1515,1516,1517,1518,1519,1520,1521,1522,1524,1525,1527,1528,1530,1532,1533,1535,1536,1537,1538,1539,1540,1542,1543,1544,1545,1546,1547,1549,1551,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1567,1568,1569,1570,1571,1572,1573,1575,1579,1583,1584,1585,1586,1587,1588,1592,1593,1594,1595,1597,1599,1601,1603,1605,1606,1607,1608,1610,1612,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1628,1629,1630,1631,1633,1635,1636,1638,1639,1641,1643,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1658,1659,1660,1661,1663,1664,1665,1666,1667,1669,1671,1673,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1773,1776,1779,1782,1796,1807,1817,1847,1874,1883,1958,2355,2360,2388,2406,2442,2448,2454,2477,2618,2638,2644,2648,2654,2691,2703,2769,2793,2862,2881,2907,2916", "endColumns": "54,44,48,40,54,58,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,530,600,661,736,812,889,967,1052,1134,1210,1286,1363,1441,1547,1653,1732,1812,1869,1927,2001,2076,2141,2207,2267,2328,2400,2473,2540,2608,2667,2726,2785,2844,2903,2957,3011,3064,3118,3172,3226,3280,3354,3433,3506,3580,3651,3723,3795,3868,3925,3983,4056,4130,4204,4279,4351,4424,4494,4565,4625,4686,4755,4824,4894,4968,5044,5108,5185,5261,5338,5403,5472,5549,5624,5693,5761,5838,5904,5965,6062,6127,6196,6295,6366,6425,6483,6540,6599,6663,6734,6806,6878,6950,7022,7089,7157,7225,7284,7347,7411,7501,7592,7652,7718,7785,7851,7921,7985,8038,8105,8166,8233,8346,8404,8467,8532,8597,8672,8745,8817,8866,8927,8988,9049,9111,9175,9239,9303,9368,9431,9491,9552,9618,9677,9737,9799,9870,9930,9998,10084,10171,10261,10348,10436,10518,10601,10691,10782,10834,10892,10937,11003,11067,11124,11181,11235,11292,11340,11389,11440,11474,11521,11570,11616,11648,11712,11774,11834,11891,11965,12035,12113,12167,12237,12322,12370,12416,12477,12540,12606,12670,12741,12804,12869,12933,12994,13055,13107,13180,13254,13323,13398,13472,13546,13687,13757,13810,13888,13978,14066,14162,14252,14834,14923,15170,15451,15703,15988,16381,16858,17080,17302,17578,17805,18035,18265,18495,18725,18952,19371,19597,20022,20252,20680,20899,21182,21390,21521,21748,22174,22399,22826,23047,23472,23592,23868,24169,24493,24784,25098,25235,25366,25471,25713,25880,26084,26292,26563,26675,26787,26892,27009,27223,27369,27509,27595,27943,28031,28277,28695,28944,29026,29124,29741,29841,30093,30517,30772,30866,30955,31192,33244,33486,33588,33841,36025,47058,48574,59705,61233,62990,63616,64036,65097,66362,66618,66854,67401,67895,68500,68698,69278,69842,70217,70335,70873,71030,71226,71499,71755,71925,72066,72130,72495,72862,73538,73802,74140,74493,74587,74773,75079,75341,75466,75593,75832,76043,76162,76355,76532,76987,77168,77290,77549,77662,77849,77951,78058,78187,78462,78970,79466,80343,80637,81207,81356,82088,82260,82344,82680,82772,83050,88459,94011,94073,94703,95317,95408,95521,95750,95910,96062,96233,96399,96568,96735,96898,97141,97311,97484,97655,97929,98128,98333,98663,98747,98843,98939,99037,99137,99239,99341,99443,99545,99647,99747,99843,99955,100084,100207,100338,100469,100567,100681,100775,100915,101049,101145,101257,101357,101473,101569,101681,101781,101921,102057,102221,102351,102509,102659,102800,102944,103079,103191,103341,103469,103597,103733,103865,103995,104125,104237,104377,104523,104667,104805,104871,104961,105037,105141,105231,105333,105441,105549,105649,105729,105821,105919,106029,106081,106159,106265,106357,106461,106571,106693,106856,107013,107093,107193,107283,107393,107483,107724,107818,107924,108016,108116,108228,108342,108458,108574,108668,108782,108894,108996,109116,109238,109320,109424,109544,109670,109768,109862,109950,110062,110178,110300,110412,110587,110703,110789,110881,110993,111117,111184,111310,111378,111506,111650,111778,111847,111942,112057,112170,112269,112378,112489,112600,112701,112806,112906,113036,113127,113250,113344,113456,113542,113646,113742,113830,113948,114052,114156,114282,114370,114478,114578,114668,114778,114862,114964,115048,115102,115166,115272,115358,115468,115552,115672,120816,120934,121049,121181,121896,122588,123105,124704,126237,126625,131360,151622,151882,153392,154425,156438,156700,157056,157886,164668,165802,166096,166319,166646,168696,169344,173195,174397,178476,179691,181100,181574"}, "to": {"startLines": "65,148,149,371,418,419,420,426,427,428,429,430,431,432,435,436,437,438,439,440,441,442,443,444,449,450,461,462,463,464,465,466,467,468,479,480,481,482,483,484,485,486,487,488,489,490,491,492,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,603,604,626,627,628,629,630,631,632,658,659,660,661,662,663,664,665,701,702,703,704,709,720,721,726,748,755,756,757,758,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,864,872,873,874,875,876,877,885,886,890,894,898,903,909,916,920,924,929,933,937,941,945,949,953,959,963,969,973,979,983,988,992,995,999,1005,1009,1015,1019,1025,1028,1032,1036,1040,1044,1048,1049,1050,1051,1054,1057,1060,1063,1067,1068,1069,1070,1071,1074,1076,1078,1080,1085,1086,1090,1096,1100,1101,1103,1114,1115,1119,1125,1129,1130,1131,1135,1162,1166,1167,1171,1199,1368,1394,1563,1589,1620,1628,1634,1648,1670,1675,1680,1690,1699,1708,1712,1719,1727,1734,1735,1744,1747,1750,1754,1758,1762,1765,1766,1771,1776,1786,1791,1798,1804,1805,1808,1812,1817,1819,1821,1824,1827,1829,1833,1836,1843,1846,1849,1853,1855,1859,1861,1863,1865,1869,1877,1885,1897,1903,1912,1915,1926,1929,1930,1935,1936,1965,2034,2104,2105,2115,2124,2276,2278,2282,2285,2288,2291,2294,2297,2300,2303,2307,2310,2313,2316,2320,2323,2327,2331,2332,2333,2334,2335,2336,2337,2338,2339,2340,2341,2342,2343,2344,2345,2346,2347,2348,2349,2350,2351,2353,2355,2356,2357,2358,2359,2360,2361,2362,2364,2365,2367,2368,2370,2372,2373,2375,2376,2377,2378,2379,2380,2382,2383,2384,2385,2386,2398,2400,2402,2404,2405,2406,2407,2408,2409,2410,2411,2412,2413,2414,2415,2416,2418,2419,2420,2421,2422,2423,2424,2426,2430,2442,2443,2444,2445,2446,2447,2451,2452,2453,2454,2456,2458,2460,2462,2464,2465,2466,2467,2469,2471,2473,2474,2475,2476,2477,2478,2479,2480,2481,2482,2483,2484,2487,2488,2489,2490,2492,2494,2495,2497,2498,2500,2502,2504,2505,2506,2507,2508,2509,2510,2511,2512,2513,2514,2515,2517,2518,2519,2520,2522,2523,2524,2525,2526,2528,2530,2532,2534,2535,2536,2537,2538,2539,2540,2541,2542,2543,2544,2545,2546,2547,2548,2554,2629,2632,2635,2638,2652,2676,2718,2747,2774,2783,2845,3209,3258,4006,4360,4384,4390,4419,4440,4564,4744,4750,4906,4932,4999,5077,5180,5216,5350,5362,5388", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2884,6036,6081,15841,17686,17741,17800,18195,18259,18329,18390,18465,18541,18618,18856,18941,19023,19099,19175,19252,19330,19436,19542,19621,19950,20007,20867,20941,21016,21081,21147,21207,21268,21340,21892,21959,22027,22086,22145,22204,22263,22322,22376,22430,22483,22537,22591,22645,22900,22974,23053,23126,23200,23271,23343,23415,23488,23545,23603,23676,23750,23824,23899,23971,24044,24114,24185,24245,24306,24375,24444,24514,24588,24664,24728,24805,24881,24958,25023,25092,25169,25244,25313,25381,25458,25524,25585,25682,25747,25816,25915,25986,26045,26103,26160,26219,26283,26354,26426,26498,26570,26642,26709,26777,26845,26904,26967,27031,27121,27212,27272,27338,27405,27471,27541,27605,27658,27725,27786,27853,27966,28024,28087,28152,28217,28292,28365,28437,28486,28547,28608,28669,28731,28795,28859,28923,28988,29051,29111,29172,29238,29297,29357,29419,29490,29550,30249,30335,31667,31757,31844,31932,32014,32097,32187,33912,33964,34022,34067,34133,34197,34254,34311,36488,36545,36593,36642,36897,37418,37465,37723,38894,39242,39306,39368,39428,39749,39823,39893,39971,40025,40095,40180,40228,40274,40335,40398,40464,40528,40599,40662,40727,40791,40852,40913,40965,41038,41112,41181,41256,41330,41404,41545,48488,49018,49096,49186,49274,49370,49460,50042,50131,50378,50659,50911,51196,51589,52066,52288,52510,52786,53013,53243,53473,53703,53933,54160,54579,54805,55230,55460,55888,56107,56390,56598,56729,56956,57382,57607,58034,58255,58680,58800,59076,59377,59701,59992,60306,60443,60574,60679,60921,61088,61292,61500,61771,61883,61995,62100,62217,62431,62577,62717,62803,63151,63239,63485,63903,64152,64234,64332,64924,65024,65276,65700,65955,66049,66138,66375,68399,68641,68743,68996,71152,81593,83109,93648,95176,96933,97559,97979,99040,100305,100561,100797,101344,101838,102443,102641,103221,103785,104160,104278,104816,104973,105169,105442,105698,105868,106009,106073,106438,106805,107481,107745,108083,108436,108530,108716,109022,109284,109409,109536,109775,109986,110105,110298,110475,110930,111111,111233,111492,111605,111792,111894,112001,112130,112405,112913,113409,114286,114580,115150,115299,116031,116203,116287,116623,116715,118781,124027,129416,129478,130056,130640,138587,138700,138929,139089,139241,139412,139578,139747,139914,140077,140320,140490,140663,140834,141108,141307,141512,141842,141926,142022,142118,142216,142316,142418,142520,142622,142724,142826,142926,143022,143134,143263,143386,143517,143648,143746,143860,143954,144094,144228,144324,144436,144536,144652,144748,144860,144960,145100,145236,145400,145530,145688,145838,145979,146123,146258,146370,146520,146648,146776,146912,147044,147174,147304,147416,148314,148460,148604,148742,148808,148898,148974,149078,149168,149270,149378,149486,149586,149666,149758,149856,149966,150018,150096,150202,150294,150398,150508,150630,150793,151348,151428,151528,151618,151728,151818,152059,152153,152259,152351,152451,152563,152677,152793,152909,153003,153117,153229,153331,153451,153573,153655,153759,153879,154005,154103,154197,154285,154397,154513,154635,154747,154922,155038,155124,155216,155328,155452,155519,155645,155713,155841,155985,156113,156182,156277,156392,156505,156604,156713,156824,156935,157036,157141,157241,157371,157462,157585,157679,157791,157877,157981,158077,158165,158283,158387,158491,158617,158705,158813,158913,159003,159113,159197,159299,159383,159437,159501,159607,159693,159803,159887,160291,162907,163025,163140,163220,163581,164347,165751,167095,168456,168844,171619,181708,183685,214876,228303,229054,229316,230163,230542,234820,242472,242701,247605,248615,250567,253237,257484,258875,263523,263863,265174", "endLines": "65,148,149,371,418,419,420,426,427,428,429,430,431,432,435,436,437,438,439,440,441,442,443,444,449,450,461,462,463,464,465,466,467,468,479,480,481,482,483,484,485,486,487,488,489,490,491,492,496,497,498,499,500,501,502,503,504,505,506,507,508,509,510,511,512,513,514,515,516,517,518,519,520,521,522,523,524,525,526,527,528,529,530,531,532,533,534,535,536,537,538,539,540,541,542,543,544,545,546,547,548,549,550,551,552,553,554,555,556,557,558,559,560,561,562,563,564,565,566,567,568,569,570,571,572,573,574,575,576,577,578,579,580,581,582,583,584,585,586,587,588,589,590,591,592,593,603,604,626,627,628,629,630,631,632,658,659,660,661,662,663,664,665,701,702,703,704,709,720,721,726,748,755,756,757,758,763,764,765,766,767,768,769,770,771,772,773,774,775,776,777,778,779,780,781,782,783,784,785,786,787,788,789,864,872,873,874,875,876,884,885,889,893,897,902,908,915,919,923,928,932,936,940,944,948,952,958,962,968,972,978,982,987,991,994,998,1004,1008,1014,1018,1024,1027,1031,1035,1039,1043,1047,1048,1049,1050,1053,1056,1059,1062,1066,1067,1068,1069,1070,1073,1075,1077,1079,1084,1085,1089,1095,1099,1100,1102,1113,1114,1118,1124,1128,1129,1130,1134,1161,1165,1166,1170,1198,1367,1393,1562,1588,1619,1627,1633,1647,1669,1674,1679,1689,1698,1707,1711,1718,1726,1733,1734,1743,1746,1749,1753,1757,1761,1764,1765,1770,1775,1785,1790,1797,1803,1804,1807,1811,1816,1818,1820,1823,1826,1828,1832,1835,1842,1845,1848,1852,1854,1858,1860,1862,1864,1868,1876,1884,1896,1902,1911,1914,1925,1928,1929,1934,1935,1940,2033,2103,2104,2114,2123,2124,2277,2281,2284,2287,2290,2293,2296,2299,2302,2306,2309,2312,2315,2319,2322,2326,2330,2331,2332,2333,2334,2335,2336,2337,2338,2339,2340,2341,2342,2343,2344,2345,2346,2347,2348,2349,2350,2352,2354,2355,2356,2357,2358,2359,2360,2361,2363,2364,2366,2367,2369,2371,2372,2374,2375,2376,2377,2378,2379,2381,2382,2383,2384,2385,2386,2399,2401,2403,2404,2405,2406,2407,2408,2409,2410,2411,2412,2413,2414,2415,2417,2418,2419,2420,2421,2422,2423,2425,2429,2433,2442,2443,2444,2445,2446,2450,2451,2452,2453,2455,2457,2459,2461,2463,2464,2465,2466,2468,2470,2472,2473,2474,2475,2476,2477,2478,2479,2480,2481,2482,2483,2486,2487,2488,2489,2491,2493,2494,2496,2497,2499,2501,2503,2504,2505,2506,2507,2508,2509,2510,2511,2512,2513,2514,2516,2517,2518,2519,2521,2522,2523,2524,2525,2527,2529,2531,2533,2534,2535,2536,2537,2538,2539,2540,2541,2542,2543,2544,2545,2546,2547,2548,2628,2631,2634,2637,2651,2657,2685,2746,2773,2782,2844,3203,3212,3285,4023,4383,4389,4395,4439,4563,4583,4749,4753,4911,4966,5010,5142,5199,5270,5361,5387,5394", "endColumns": "54,44,48,40,54,58,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2934,6076,6125,15877,17736,17795,17857,18254,18324,18385,18460,18536,18613,18691,18936,19018,19094,19170,19247,19325,19431,19537,19616,19696,20002,20060,20936,21011,21076,21142,21202,21263,21335,21408,21954,22022,22081,22140,22199,22258,22317,22371,22425,22478,22532,22586,22640,22694,22969,23048,23121,23195,23266,23338,23410,23483,23540,23598,23671,23745,23819,23894,23966,24039,24109,24180,24240,24301,24370,24439,24509,24583,24659,24723,24800,24876,24953,25018,25087,25164,25239,25308,25376,25453,25519,25580,25677,25742,25811,25910,25981,26040,26098,26155,26214,26278,26349,26421,26493,26565,26637,26704,26772,26840,26899,26962,27026,27116,27207,27267,27333,27400,27466,27536,27600,27653,27720,27781,27848,27961,28019,28082,28147,28212,28287,28360,28432,28481,28542,28603,28664,28726,28790,28854,28918,28983,29046,29106,29167,29233,29292,29352,29414,29485,29545,29613,30330,30417,31752,31839,31927,32009,32092,32182,32273,33959,34017,34062,34128,34192,34249,34306,34360,36540,36588,36637,36688,36926,37460,37509,37764,38921,39301,39363,39423,39480,39818,39888,39966,40020,40090,40175,40223,40269,40330,40393,40459,40523,40594,40657,40722,40786,40847,40908,40960,41033,41107,41176,41251,41325,41399,41540,41610,48536,49091,49181,49269,49365,49455,50037,50126,50373,50654,50906,51191,51584,52061,52283,52505,52781,53008,53238,53468,53698,53928,54155,54574,54800,55225,55455,55883,56102,56385,56593,56724,56951,57377,57602,58029,58250,58675,58795,59071,59372,59696,59987,60301,60438,60569,60674,60916,61083,61287,61495,61766,61878,61990,62095,62212,62426,62572,62712,62798,63146,63234,63480,63898,64147,64229,64327,64919,65019,65271,65695,65950,66044,66133,66370,68394,68636,68738,68991,71147,81588,83104,93643,95171,96928,97554,97974,99035,100300,100556,100792,101339,101833,102438,102636,103216,103780,104155,104273,104811,104968,105164,105437,105693,105863,106004,106068,106433,106800,107476,107740,108078,108431,108525,108711,109017,109279,109404,109531,109770,109981,110100,110293,110470,110925,111106,111228,111487,111600,111787,111889,111996,112125,112400,112908,113404,114281,114575,115145,115294,116026,116198,116282,116618,116710,116988,124022,129411,129473,130051,130635,130726,138695,138924,139084,139236,139407,139573,139742,139909,140072,140315,140485,140658,140829,141103,141302,141507,141837,141921,142017,142113,142211,142311,142413,142515,142617,142719,142821,142921,143017,143129,143258,143381,143512,143643,143741,143855,143949,144089,144223,144319,144431,144531,144647,144743,144855,144955,145095,145231,145395,145525,145683,145833,145974,146118,146253,146365,146515,146643,146771,146907,147039,147169,147299,147411,147551,148455,148599,148737,148803,148893,148969,149073,149163,149265,149373,149481,149581,149661,149753,149851,149961,150013,150091,150197,150289,150393,150503,150625,150788,150945,151423,151523,151613,151723,151813,152054,152148,152254,152346,152446,152558,152672,152788,152904,152998,153112,153224,153326,153446,153568,153650,153754,153874,154000,154098,154192,154280,154392,154508,154630,154742,154917,155033,155119,155211,155323,155447,155514,155640,155708,155836,155980,156108,156177,156272,156387,156500,156599,156708,156819,156930,157031,157136,157236,157366,157457,157580,157674,157786,157872,157976,158072,158160,158278,158382,158486,158612,158700,158808,158908,158998,159108,159192,159294,159378,159432,159496,159602,159688,159798,159882,160002,162902,163020,163135,163215,163576,163809,164859,167090,168451,168839,171614,181518,181838,185037,215443,229049,229311,229511,230537,234815,235421,242696,242847,247815,249693,250874,256258,258223,261001,263858,265169,265372"}}, {"source": "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "1958,1962", "startColumns": "4,4", "startOffsets": "118431,118612", "endLines": "1961,1964", "endColumns": "12,12", "endOffsets": "118607,118776"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f87704cc6ac259b753f491455f413615\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "707,708,716,723,724,743,744,745,746,747", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "36810,36850,37218,37556,37611,38628,38682,38734,38783,38844", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "36845,36892,37256,37606,37653,38677,38729,38778,38839,38889"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\aa55b2079cbc673a6a445c1850daa153\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "750", "startColumns": "4", "startOffsets": "38971", "endColumns": "42", "endOffsets": "39009"}}, {"source": "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\google_mobile_ads\\intermediates\\packaged_res\\debug\\packageDebugResources\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,102,146,189,232,276,322,364,426,490,534,593,655,710,768,826,880,930,1008,1068,1158,1245,1289,1331,1390,1437,1514,1565,1616", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,32", "endColumns": "46,43,42,42,43,45,41,61,63,43,58,61,54,57,57,53,49,77,59,89,86,43,41,58,46,76,50,50,22", "endOffsets": "97,141,184,227,271,317,359,421,485,529,588,650,705,763,821,875,925,1003,1063,1153,1240,1284,1326,1385,1432,1509,1560,1611,1734"}, "to": {"startLines": "469,470,471,472,473,474,475,476,477,478,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,5177", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21413,21460,21504,21547,21590,21634,21680,21722,21784,21848,30585,30644,30706,30761,30819,30877,30931,30981,31059,31119,31209,31296,31340,31382,31441,31488,31565,31616,257361", "endLines": "469,470,471,472,473,474,475,476,477,478,608,609,610,611,612,613,614,615,616,617,618,619,620,621,622,623,624,625,5179", "endColumns": "46,43,42,42,43,45,41,61,63,43,58,61,54,57,57,53,49,77,59,89,86,43,41,58,46,76,50,50,22", "endOffsets": "21455,21499,21542,21585,21629,21675,21717,21779,21843,21887,30639,30701,30756,30814,30872,30926,30976,31054,31114,31204,31291,31335,31377,31436,31483,31560,31611,31662,257479"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fc83ed23dd70c2212cc258f35335ac1d\\transformed\\jetified-play-services-ads-lite-23.6.0\\res\\values\\values.xml", "from": {"startLines": "4,14", "startColumns": "0,0", "startOffsets": "167,661", "endLines": "11,20", "endColumns": "8,20", "endOffsets": "560,836"}, "to": {"startLines": "2434,2669", "startColumns": "4,4", "startOffsets": "150950,164167", "endLines": "2441,2675", "endColumns": "8,20", "endOffsets": "151343,164342"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "303,433,434,451,452,493,494,596,597,598,599,600,601,602,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,711,712,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,762,791,792,793,794,795,796,797,865,2387,2388,2392,2393,2397,2549,2550,3213,3248,4028,4061,4091,4124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13226,18696,18768,20065,20130,22699,22768,29761,29831,29899,29971,30041,30102,30176,32501,32562,32623,32685,32749,32811,32872,32940,33040,33100,33166,33239,33308,33365,33417,34365,34437,34513,34578,34637,34696,34756,34816,34876,34936,34996,35056,35116,35176,35236,35296,35355,35415,35475,35535,35595,35655,35715,35775,35835,35895,35955,36014,36074,36134,36193,36252,36311,36370,36429,36997,37032,37769,37824,37887,37942,38000,38058,38119,38182,38239,38290,38340,38401,38458,38524,38558,38593,39679,41698,41765,41837,41906,41975,42049,42121,48541,147556,147673,147874,147984,148185,160007,160079,181843,183384,215597,217328,218328,219010", "endLines": "303,433,434,451,452,493,494,596,597,598,599,600,601,602,636,637,638,639,640,641,642,643,644,645,646,647,648,649,650,666,667,668,669,670,671,672,673,674,675,676,677,678,679,680,681,682,683,684,685,686,687,688,689,690,691,692,693,694,695,696,697,698,699,700,711,712,727,728,729,730,731,732,733,734,735,736,737,738,739,740,741,742,762,791,792,793,794,795,796,797,865,2387,2391,2392,2396,2397,2549,2550,3218,3257,4060,4081,4123,4129", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "13281,18763,18851,20125,20191,22763,22826,29826,29894,29966,30036,30097,30171,30244,32557,32618,32680,32744,32806,32867,32935,33035,33095,33161,33234,33303,33360,33412,33474,34432,34508,34573,34632,34691,34751,34811,34871,34931,34991,35051,35111,35171,35231,35291,35350,35410,35470,35530,35590,35650,35710,35770,35830,35890,35950,36009,36069,36129,36188,36247,36306,36365,36424,36483,37027,37062,37819,37882,37937,37995,38053,38114,38177,38234,38285,38335,38396,38453,38519,38553,38588,38623,39744,41760,41832,41901,41970,42044,42116,42204,48607,147668,147869,147979,148180,148309,160074,160141,182041,183680,217323,218004,219005,219172"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e5aa58ad971ef0b034990717d52ef9e6\\transformed\\jetified-play-services-ads-23.6.0\\res\\values\\values.xml", "from": {"startLines": "5,7,10,13,16,19,21,23,25,27,29,31,33,35,37,39,41,42,43,44,45,46,47,48", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "167,224,373,620,869,1145,1284,1441,1653,1857,2085,2309,2572,2743,2926,3145,3330,3368,3441,3475,3510,3559,3623,3658", "endLines": "5,7,12,15,18,20,22,24,26,28,30,32,34,36,38,40,41,42,43,44,45,46,47,50", "endColumns": "55,45,11,11,11,20,27,51,19,86,68,62,17,24,81,48,37,72,33,34,48,63,34,11", "endOffsets": "222,269,619,868,1144,1283,1440,1652,1856,2084,2308,2571,2742,2925,3144,3329,3367,3440,3474,3509,3558,3622,3657,3822"}, "to": {"startLines": "714,715,822,825,828,832,834,836,838,840,842,844,846,848,850,852,857,858,859,860,861,862,863,869", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "37108,37168,44920,45176,45434,45763,45906,46067,46283,46491,46723,46951,47218,47393,47580,47803,48132,48174,48251,48289,48328,48381,48449,48849", "endLines": "714,715,824,827,830,833,835,837,839,841,843,845,847,849,851,853,857,858,859,860,861,862,863,871", "endColumns": "59,49,11,11,11,20,27,51,19,86,68,62,17,24,81,48,41,76,37,38,52,67,38,11", "endOffsets": "37163,37213,45171,45429,45714,45901,46062,46278,46486,46718,46946,47213,47388,47575,47798,47987,48169,48246,48284,48323,48376,48444,48483,49013"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e20001cc41f5487e719977c4b5116946\\transformed\\jetified-play-services-base-18.0.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "453,454,455,456,457,458,459,460,798,799,800,801,802,803,804,805,807,808,809,810,811,812,813,814,815,4406,4980", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20196,20286,20366,20456,20546,20626,20707,20787,42209,42314,42495,42620,42727,42907,43030,43146,43416,43604,43709,43890,44015,44190,44338,44401,44463,229848,250150", "endLines": "453,454,455,456,457,458,459,460,798,799,800,801,802,803,804,805,807,808,809,810,811,812,813,814,815,4418,4998", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "20281,20361,20451,20541,20621,20702,20782,20862,42309,42490,42615,42722,42902,43025,43141,43244,43599,43704,43885,44010,44185,44333,44396,44458,44537,230158,250562"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\85879f220671a879b538e8ef16ed1744\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "790", "startColumns": "4", "startOffsets": "41615", "endColumns": "82", "endOffsets": "41693"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\185f2479ab24942c0bba65b9ff947d79\\transformed\\jetified-appcompat-resources-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2686,2702,2708,5054,5070", "startColumns": "4,4,4,4,4", "startOffsets": "164864,165289,165467,252579,252990", "endLines": "2701,2707,2717,5069,5073", "endColumns": "24,24,24,24,24", "endOffsets": "165284,165462,165746,252985,253112"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b83b8b00b8346c9e7414a1f1298f055d\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "421,495,651,652,653,654,655,656,657,717,718,719,760,761,816,818,831,854,866,867,868,1941,2125,2128,2134,2140,2143,2149,2153,2156,2163,2169,2172,2178,2183,2188,2195,2197,2203,2209,2217,2222,2229,2234,2240,2244,2251,2255,2261,2267,2270,2274,2275,3204,3237,3986,4024,4396,4682,4754,4818,4828,4838,4845,4851,4967,5143,5160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17862,22831,33479,33543,33598,33666,33733,33798,33855,37261,37309,37357,39553,39616,44542,44648,45719,47992,48612,48751,48801,116993,130731,130836,131081,131419,131565,131905,132117,132280,132687,133025,133148,133487,133726,133983,134354,134414,134752,135038,135487,135779,136167,136472,136816,137061,137391,137598,137866,138139,138283,138484,138531,181523,182983,214147,215448,229516,239793,242852,244777,245059,245364,245626,245886,249698,256263,256793", "endLines": "421,495,651,652,653,654,655,656,657,717,718,719,760,761,816,818,831,856,866,867,868,1957,2127,2133,2139,2142,2148,2152,2155,2162,2168,2171,2177,2182,2187,2194,2196,2202,2208,2216,2221,2228,2233,2239,2243,2250,2254,2260,2266,2269,2273,2274,2275,3208,3247,4005,4027,4405,4689,4817,4827,4837,4844,4850,4893,4979,5159,5176", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "17930,22895,33538,33593,33661,33728,33793,33850,33907,37304,37352,37413,39611,39674,44575,44700,45758,48127,48746,48796,48844,118426,130831,131076,131414,131560,131900,132112,132275,132682,133020,133143,133482,133721,133978,134349,134409,134747,135033,135482,135774,136162,136467,136811,137056,137386,137593,137861,138134,138278,138479,138526,138582,181703,183379,214871,215592,229843,240036,244772,245054,245359,245621,245881,247304,250145,256788,257356"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\79275990ee9dddfd68bc7c9d7157e0cd\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "605,606,607,633,634,635,710,4912", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "30422,30481,30529,32278,32353,32429,36931,247820", "endLines": "605,606,607,633,634,635,710,4931", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "30476,30524,30580,32348,32424,32496,36992,248610"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e60d7cc8f585e105683d15c0883739b4\\transformed\\jetified-activity-1.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "722,751", "startColumns": "4,4", "startOffsets": "37514,39014", "endColumns": "41,59", "endOffsets": "37551,39069"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\84addddb59162e1cea52976d5f2c6cc1\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "753", "startColumns": "4", "startOffsets": "39128", "endColumns": "49", "endOffsets": "39173"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f43a8a95ab2f768e2215dacb56c18080\\transformed\\constraintlayout-2.1.4\\res\\values\\values.xml", "from": {"startLines": "2,9,10,15,16,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,42,43,44,45,55,63,64,65,70,71,76,81,82,83,88,89,94,95,100,101,102,108,109,110,115,121,122,123,124,130,131,132,133,136,139,142,143,146,149,150,151,152,153,156,159,160,161,162,168,173,176,179,180,181,186,187,188,191,194,195,198,201,204,207,208,209,212,215,216,221,222,228,233,236,239,240,241,242,243,244,245,246,247,248,249,250,266,272,273,274,275,276,283,289,290,291,294,299,300,308,309,310,311,312,313,314,315,324,325,326,332,333,339,343,344,345,346,347,356,360,361,362,380,566,694,700,704,874,1026,1039,1055,1080,1103,1106,1109,1112,1141,1168,1185,1271,1279,1292,1308,1312,1342,1355,1359,1369,1379,1423,1436,1440,1443,1459,1500,1535,1542,1559", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,339,395,581,642,933,985,1035,1088,1136,1187,1242,1302,1367,1426,1488,1540,1601,1663,1709,1842,1894,1944,1995,2402,2714,2759,2818,3015,3072,3267,3448,3502,3559,3751,3809,4005,4061,4255,4312,4363,4585,4637,4692,4882,5098,5148,5200,5256,5462,5523,5583,5653,5786,5917,6045,6113,6242,6368,6430,6493,6561,6628,6751,6876,6943,7008,7073,7362,7543,7664,7785,7851,7918,8128,8197,8263,8388,8514,8581,8707,8834,8959,9086,9142,9207,9333,9456,9521,9729,9796,10084,10264,10384,10504,10569,10631,10693,10757,10819,10878,10938,10999,11060,11119,11179,11870,12121,12172,12221,12269,12327,12619,12849,12896,12956,13062,13242,13296,13631,13685,13741,13787,13834,13885,13944,13996,14326,14385,14439,14677,14732,15022,15161,15207,15262,15307,15351,15699,15836,15877,15922,16859,25449,31222,31597,31764,39466,46265,46962,47713,48588,49458,49524,49603,49678,51026,52013,52976,56913,57318,57789,58580,58743,60104,60668,60821,61280,61698,63711,64248,64398,64518,65165,66854,68275,68628,69370", "endLines": "8,9,14,15,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,41,42,43,44,54,62,63,64,69,70,75,80,81,82,87,88,93,94,99,100,101,107,108,109,114,120,121,122,123,129,130,131,132,135,138,141,142,145,148,149,150,151,152,155,158,159,160,161,167,172,175,178,179,180,185,186,187,190,193,194,197,200,203,206,207,208,211,214,215,220,221,227,232,235,238,239,240,241,242,243,244,245,246,247,248,249,265,271,272,273,274,275,282,288,289,290,293,298,299,307,308,309,310,311,312,313,314,323,324,325,331,332,338,342,343,344,345,346,355,359,360,361,379,565,693,699,703,873,1025,1038,1054,1079,1102,1105,1108,1111,1140,1167,1184,1270,1278,1291,1307,1311,1341,1354,1358,1368,1378,1422,1435,1439,1442,1458,1499,1534,1541,1558,1561", "endColumns": "11,55,11,60,11,51,49,52,47,50,54,59,64,58,61,51,60,61,45,11,51,49,50,11,11,44,58,11,56,11,11,53,56,11,57,11,55,11,56,50,11,51,54,11,11,49,51,55,11,60,59,69,11,11,11,67,11,11,61,62,67,66,11,11,66,64,64,11,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,55,64,11,11,64,11,66,11,11,11,11,64,61,61,63,61,58,59,60,60,58,59,11,11,50,48,47,57,11,11,46,59,11,11,53,11,53,55,45,46,50,58,51,11,58,53,11,54,11,11,45,54,44,43,11,11,40,44,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "334,390,576,637,928,980,1030,1083,1131,1182,1237,1297,1362,1421,1483,1535,1596,1658,1704,1837,1889,1939,1990,2397,2709,2754,2813,3010,3067,3262,3443,3497,3554,3746,3804,4000,4056,4250,4307,4358,4580,4632,4687,4877,5093,5143,5195,5251,5457,5518,5578,5648,5781,5912,6040,6108,6237,6363,6425,6488,6556,6623,6746,6871,6938,7003,7068,7357,7538,7659,7780,7846,7913,8123,8192,8258,8383,8509,8576,8702,8829,8954,9081,9137,9202,9328,9451,9516,9724,9791,10079,10259,10379,10499,10564,10626,10688,10752,10814,10873,10933,10994,11055,11114,11174,11865,12116,12167,12216,12264,12322,12614,12844,12891,12951,13057,13237,13291,13626,13680,13736,13782,13829,13880,13939,13991,14321,14380,14434,14672,14727,15017,15156,15202,15257,15302,15346,15694,15831,15872,15917,16854,25444,31217,31592,31759,39461,46260,46957,47708,48583,49453,49519,49598,49673,51021,52008,52971,56908,57313,57784,58575,58738,60099,60663,60816,61275,61693,63706,64243,64393,64513,65160,66849,68270,68623,69365,69466"}, "to": {"startLines": "2,9,11,16,17,25,26,27,28,29,30,31,32,33,34,35,36,37,38,40,44,45,46,47,57,66,89,90,95,96,101,106,107,108,113,114,119,120,125,126,127,133,134,135,140,146,147,150,151,157,158,159,160,163,166,169,170,173,176,177,178,179,180,183,186,187,188,189,195,200,203,206,207,208,213,214,215,218,221,222,225,228,231,234,235,236,239,242,243,248,249,255,260,263,266,267,268,269,270,271,272,273,274,275,276,277,293,299,300,301,302,304,311,317,318,319,322,327,328,336,337,338,368,369,370,372,373,382,383,384,390,391,397,401,402,403,404,405,414,713,749,3219,3286,3451,3579,3585,3589,3738,3973,4130,4146,4171,4194,4197,4200,4203,4230,4257,4274,4584,4592,4605,4621,4625,4655,4668,4672,4690,4700,4894,5050,5074,5200,5271,5308,5343,5395,5412", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,389,504,690,751,1042,1094,1144,1197,1245,1296,1351,1411,1476,1535,1597,1649,1710,1772,1879,2012,2064,2114,2165,2572,2939,3595,3654,3851,3908,4103,4284,4338,4395,4587,4645,4841,4897,5091,5148,5199,5421,5473,5528,5718,5934,5984,6130,6186,6392,6453,6513,6583,6716,6847,6975,7043,7172,7298,7360,7423,7491,7558,7681,7806,7873,7938,8003,8292,8473,8594,8715,8781,8848,9058,9127,9193,9318,9444,9511,9637,9764,9889,10016,10072,10137,10263,10386,10451,10659,10726,11014,11194,11314,11434,11499,11561,11623,11687,11749,11808,11868,11929,11990,12049,12109,12769,13020,13071,13120,13168,13286,13578,13808,13855,13915,14021,14201,14255,14590,14644,14700,15684,15731,15782,15882,15934,16264,16323,16377,16615,16670,16872,17011,17057,17112,17157,17201,17549,37067,38926,182046,185042,191160,196825,197200,197367,202597,213450,219177,219928,220782,221652,221718,221797,221872,222656,223547,224366,235426,235831,236302,237093,237256,238617,239181,239334,240041,240459,247309,252429,253117,258228,261006,261749,263170,265377,266119", "endLines": "8,9,15,16,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,43,44,45,46,56,64,66,89,94,95,100,105,106,107,112,113,118,119,124,125,126,132,133,134,139,145,146,147,150,156,157,158,159,162,165,168,169,172,175,176,177,178,179,182,185,186,187,188,194,199,202,205,206,207,212,213,214,217,220,221,224,227,230,233,234,235,238,241,242,247,248,254,259,262,265,266,267,268,269,270,271,272,273,274,275,276,292,298,299,300,301,302,310,316,317,318,321,326,327,335,336,337,338,368,369,370,372,381,382,383,389,390,396,400,401,402,403,404,413,417,713,749,3236,3450,3578,3584,3588,3737,3882,3985,4145,4170,4193,4196,4199,4202,4229,4256,4273,4359,4591,4604,4620,4624,4654,4667,4671,4681,4699,4743,4905,5053,5076,5215,5307,5342,5349,5411,5414", "endColumns": "11,55,11,60,11,51,49,52,47,50,54,59,64,58,61,51,60,61,45,11,51,49,50,11,11,44,58,11,56,11,11,53,56,11,57,11,55,11,56,50,11,51,54,11,11,49,51,55,11,60,59,69,11,11,11,67,11,11,61,62,67,66,11,11,66,64,64,11,11,11,11,65,66,11,68,65,11,11,66,11,11,11,11,55,64,11,11,64,11,66,11,11,11,11,64,61,61,63,61,58,59,60,60,58,59,11,11,50,48,47,57,11,11,46,59,11,11,53,11,53,55,45,46,50,58,51,11,58,53,11,54,11,11,45,54,44,43,11,11,40,44,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "384,440,685,746,1037,1089,1139,1192,1240,1291,1346,1406,1471,1530,1592,1644,1705,1767,1813,2007,2059,2109,2160,2567,2879,2979,3649,3846,3903,4098,4279,4333,4390,4582,4640,4836,4892,5086,5143,5194,5416,5468,5523,5713,5929,5979,6031,6181,6387,6448,6508,6578,6711,6842,6970,7038,7167,7293,7355,7418,7486,7553,7676,7801,7868,7933,7998,8287,8468,8589,8710,8776,8843,9053,9122,9188,9313,9439,9506,9632,9759,9884,10011,10067,10132,10258,10381,10446,10654,10721,11009,11189,11309,11429,11494,11556,11618,11682,11744,11803,11863,11924,11985,12044,12104,12764,13015,13066,13115,13163,13221,13573,13803,13850,13910,14016,14196,14250,14585,14639,14695,14741,15726,15777,15836,15929,16259,16318,16372,16610,16665,16867,17006,17052,17107,17152,17196,17544,17681,37103,38966,182978,191155,196820,197195,197362,202592,208718,214142,219923,220777,221647,221713,221792,221867,222651,223542,224361,228298,235826,236297,237088,237251,238612,239176,239329,239788,240454,242467,247600,252574,253232,258870,261744,263165,263518,266114,266215"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8f2d61afc8b21a772bd0b7bc042cb3cc\\transformed\\work-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}, "to": {"startLines": "422,423,424,425", "startColumns": "4,4,4,4", "startOffsets": "17935,18000,18070,18134", "endColumns": "64,69,63,60", "endOffsets": "17995,18065,18129,18190"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1c8746a36ac065afed39d95b2852a559\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "706,725,754,4082,4087", "startColumns": "4,4,4,4,4", "startOffsets": "36753,37658,39178,218009,218179", "endLines": "706,725,754,4086,4090", "endColumns": "56,64,63,24,24", "endOffsets": "36805,37718,39237,218174,218323"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1186d146da5ef23629d7bf94e5a0d382\\transformed\\jetified-play-services-basement-18.4.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "759,806", "startColumns": "4,4", "startOffsets": "39485,43249", "endColumns": "67,166", "endOffsets": "39548,43411"}}]}]}