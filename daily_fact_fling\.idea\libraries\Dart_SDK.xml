<component name="libraryTable">
  <library name="Dart SDK">
    <CLASSES>
      <root url="file://f:\Projetos\fvm\versions\3.32.4/bin/cache/dart-sdk/lib/async" />
      <root url="file://f:\Projetos\fvm\versions\3.32.4/bin/cache/dart-sdk/lib/collection" />
      <root url="file://f:\Projetos\fvm\versions\3.32.4/bin/cache/dart-sdk/lib/convert" />
      <root url="file://f:\Projetos\fvm\versions\3.32.4/bin/cache/dart-sdk/lib/core" />
      <root url="file://f:\Projetos\fvm\versions\3.32.4/bin/cache/dart-sdk/lib/developer" />
      <root url="file://f:\Projetos\fvm\versions\3.32.4/bin/cache/dart-sdk/lib/html" />
      <root url="file://f:\Projetos\fvm\versions\3.32.4/bin/cache/dart-sdk/lib/io" />
      <root url="file://f:\Projetos\fvm\versions\3.32.4/bin/cache/dart-sdk/lib/isolate" />
      <root url="file://f:\Projetos\fvm\versions\3.32.4/bin/cache/dart-sdk/lib/math" />
      <root url="file://f:\Projetos\fvm\versions\3.32.4/bin/cache/dart-sdk/lib/mirrors" />
      <root url="file://f:\Projetos\fvm\versions\3.32.4/bin/cache/dart-sdk/lib/typed_data" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>