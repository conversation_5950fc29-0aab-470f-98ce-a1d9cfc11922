{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "F:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "F:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "F:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/bin/ctest.exe", "root": "F:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-29400f1a6c4d547c41ad.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-271e18415d0aa4cbea41.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-2639c07f08811d292ed0.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-271e18415d0aa4cbea41.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-2639c07f08811d292ed0.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-29400f1a6c4d547c41ad.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}