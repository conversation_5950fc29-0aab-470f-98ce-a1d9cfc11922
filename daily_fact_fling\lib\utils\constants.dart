/// Game constants for Daily Fact Fling
class GameConstants {
  // Game Settings
  static const double gameSpeed = 200.0;
  static const int maxTimePerFact = 10; // seconds
  static const int pointsPerCorrectAnswer = 100;
  static const int streakMultiplier = 2;
  
  // Screen Dimensions (reference)
  static const double gameWidth = 800.0;
  static const double gameHeight = 600.0;
  
  // Animation Durations
  static const Duration factAppearDuration = Duration(milliseconds: 500);
  static const Duration factDisappearDuration = Duration(milliseconds: 300);
  static const Duration feedbackDuration = Duration(milliseconds: 1000);
  
  // Colors
  static const int primaryColor = 0xFF2196F3;
  static const int secondaryColor = 0xFF4CAF50;
  static const int errorColor = 0xFFF44336;
  static const int warningColor = 0xFFFF9800;
  
  // AdMob Test IDs (replace with production IDs before release)
  static const String testBannerAdUnitId = 'ca-app-pub-3940256099942544/6300978111';
  static const String testInterstitialAdUnitId = 'ca-app-pub-3940256099942544/1033173712';
  static const String testRewardedAdUnitId = 'ca-app-pub-3940256099942544/5224354917';
  
  // Game Balance
  static const int factsPerSession = 20;
  static const int adsFrequency = 3; // Show ad every 3 game overs
  
  // Storage Keys
  static const String highScoreKey = 'high_score';
  static const String totalGamesPlayedKey = 'total_games_played';
  static const String soundEnabledKey = 'sound_enabled';
  static const String musicEnabledKey = 'music_enabled';
}

/// Asset paths
class AssetPaths {
  // Images
  static const String imagesPath = 'assets/images/';
  static const String backgroundImage = '${imagesPath}background.png';
  static const String logoImage = '${imagesPath}logo.png';
  
  // Audio
  static const String audioPath = 'assets/audio/';
  static const String correctSound = '${audioPath}correct.wav';
  static const String incorrectSound = '${audioPath}incorrect.wav';
  static const String backgroundMusic = '${audioPath}background_music.mp3';
  
  // Data
  static const String dataPath = 'assets/data/';
  static const String factsData = '${dataPath}facts.json';
}

/// Game states
enum GameState {
  menu,
  playing,
  paused,
  gameOver,
  loading,
}

/// Fact categories
enum FactCategory {
  trueOrFalse,
  funnyOrSerious,
  oldOrNew,
  scienceOrNot,
}

/// Difficulty levels
enum DifficultyLevel {
  easy,
  medium,
  hard,
}
