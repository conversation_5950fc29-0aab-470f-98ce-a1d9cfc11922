{"inputs": ["F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\.dart_tool\\flutter_build\\0c8992056cfdfeb44696f2a8efa5014a\\app.dill", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\icon_tree_shaker.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\bin\\cache\\engine.stamp", "f:\\Projetos\\fvm\\versions\\3.32.4\\bin\\cache\\engine.stamp", "f:\\Projetos\\fvm\\versions\\3.32.4\\bin\\cache\\engine.stamp", "f:\\Projetos\\fvm\\versions\\3.32.4\\bin\\cache\\engine.stamp", "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\pubspec.yaml", "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\assets\\data\\facts.json", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\cupertino_icons-1.0.8\\assets\\CupertinoIcons.ttf", "f:\\Projetos\\fvm\\versions\\3.32.4\\bin\\cache\\artifacts\\material_fonts\\MaterialIcons-Regular.otf", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\shaders\\ink_sparkle.frag", "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\.dart_tool\\flutter_build\\0c8992056cfdfeb44696f2a8efa5014a\\native_assets.json", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\async-2.13.0\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\audioplayers-6.5.0\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\audioplayers_android-5.2.1\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\audioplayers_darwin-6.3.0\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\audioplayers_linux-4.2.1\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\audioplayers_platform_interface-7.1.1\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\audioplayers_web-5.1.1\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\audioplayers_windows-4.2.1\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\boolean_selector-2.1.2\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\characters-1.4.0\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\clock-1.1.2\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\crypto-3.0.6\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\cupertino_icons-1.0.8\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\fake_async-1.3.3\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\ffi-2.1.4\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\fixnum-1.1.1\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_lints-5.0.0\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\google_mobile_ads-5.3.1\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\http-1.4.0\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\http_parser-4.1.2\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\leak_tracker-10.0.9\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\leak_tracker_flutter_testing-3.0.9\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\leak_tracker_testing-3.0.1\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\lints-5.1.1\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\matcher-0.12.17\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\meta-1.16.0\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\nested-1.0.0\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\ordered_set-8.0.0\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.1\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider-2.1.5\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_android-2.2.17\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_linux-2.2.1\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_windows-2.3.0\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\platform-3.1.6\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\plugin_platform_interface-2.1.8\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences-2.5.3\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_linux-2.4.1\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_web-2.4.3\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_windows-2.4.1\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\source_span-1.10.1\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\sprintf-7.0.0\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\stack_trace-1.12.1\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\stream_channel-2.1.4\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\string_scanner-1.4.1\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\synchronized-3.3.1\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\term_glyph-1.2.2\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\test_api-0.7.4\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\typed_data-1.4.0\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\uuid-4.5.1\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vm_service-15.0.0\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\web-1.1.1\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter-4.13.0\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_android-4.7.0\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_wkwebview-3.22.0\\LICENSE", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\xdg_directories-1.1.0\\LICENSE", "f:\\Projetos\\fvm\\versions\\3.32.4\\bin\\cache\\pkg\\sky_engine\\LICENSE", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\LICENSE", "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\DOES_NOT_EXIST_RERUN_FOR_WILDCARD677621331"], "outputs": ["F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\data\\facts.json", "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\cupertino_icons\\assets\\CupertinoIcons.ttf", "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts\\MaterialIcons-Regular.otf", "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders\\ink_sparkle.frag", "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z", "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NativeAssetsManifest.json"]}