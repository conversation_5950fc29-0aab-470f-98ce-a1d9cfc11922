{"version": 2, "files": [{"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\image_composition.dart", "hash": "1321a68c1c89b74ac0fa77dfb0500404"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\hitboxes\\hitbox.dart", "hash": "********************************"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\elements\\rrect_element.dart", "hash": "924d36aef0ce7e62c00dd41281ffb40d"}, {"path": "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages\\cupertino_icons\\assets\\CupertinoIcons.ttf", "hash": "b93248a553f9e8bc17f1065929d5934b"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\native_assets.dart", "hash": "a78f4580594141cadc5519d96f1cee73"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\search.dart", "hash": "054abd6fdcd55ea92434e7f591f3785b"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\crypto-3.0.6\\LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\semantics.dart", "hash": "a9d4e4b3f6357c540f77101737a25e4e"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\restoration_properties.dart", "hash": "5c32703ac32c4835d961b3c55f8f8498"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\ink_highlight.dart", "hash": "4caf5b7962b2f058f4c5e33d69bf7ce7"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\ordered_set-8.0.0\\LICENSE", "hash": "a04dd23755ebb7df62766160ed784f78"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\elevated_button_theme.dart", "hash": "4988a75e1b6c9b1b2c2df65cd6318bf4"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\error_helpers.dart", "hash": "39221ca00f5f1e0af7767613695bb5d2"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\refresh.dart", "hash": "7b9a23372e88dcc5453cc21b5ea3f89e"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\geometry\\shape_intersections.dart", "hash": "a900e237927cc9c7f7e012f9e0b69127"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\magnifier.dart", "hash": "7c956712fdb0e888676c78cc537b725f"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\fractional_offset.dart", "hash": "375378eb6cf9297d6bcfe260059193a8"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\typed_data-1.4.0\\LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\spacer.dart", "hash": "977b101929ac6f80c9dab61b4f232bda"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\local.dart", "hash": "e81341d4c5ee8dc65f89ae4145cf2107"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\nested-1.0.0\\lib\\nested.dart", "hash": "5c621d343831cbb9619557942e6b7d9f"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\flow.dart", "hash": "e92d23e94e235dd63649d0e26f8314b1"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\pointer_signal_resolver.dart", "hash": "add0c413747369b7460aa14539b29791"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\card.dart", "hash": "1eb9cc478ea7bda3219d6e73edcb2929"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider-2.1.5\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\_isolates_io.dart", "hash": "825b4e65b06dcc1b8822108f596b56b0"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\service_extensions.dart", "hash": "230a3518091834c1ebaba0eda6ad491e"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\button_style_button.dart", "hash": "039f1af2b4e1859cb3852994632bac52"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences-2.5.3\\lib\\src\\shared_preferences_async.dart", "hash": "255fd9cb9db57da2261cb7553da325ab"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\mixins\\keyboard.dart", "hash": "9839ec87f4b8d5e4f0133bb532835150"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\table_border.dart", "hash": "c58ff4d729abd2cae831be9ce43e48c4"}, {"path": "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\lib\\game\\daily_fact_fling_game.dart", "hash": "4f2dabcb46cf1a220baa55f2f3f08fe0"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\hitboxes\\rectangle_hitbox.dart", "hash": "********************************"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\ffi-2.1.4\\lib\\ffi.dart", "hash": "ae66b0cbdfe2e2a5a99c5dfa48fd5399"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\transform2d.dart", "hash": "8f74d0ac1b49bf750f6c351880702e77"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\component_mixins\\pointer_move_callbacks.dart", "hash": "db84b661a09d3174ae70b59a924376d4"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\experimental\\geometry\\shapes\\polygon.dart", "hash": "********************************"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\text.dart", "hash": "efb419a0003e1d1be50b9cd93b9547a0"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\_platform_selectable_region_context_menu_io.dart", "hash": "12d3b22764f1a64ff214cabee2858a00"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\xdg_directories-1.1.0\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\devtools\\connectors\\overlay_navigation_connector.dart", "hash": "ff0ddba7976d90601abbc2981c0707a5"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\scale_effect.dart", "hash": "731ef6419ad95d9a371fa1a939fcc424"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\obb3.dart", "hash": "f7fd689f4549dd97ac670c72e4d617c6"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\uuid-4.5.1\\LICENSE", "hash": "fb92f0b8decb7b59a08fe851e030948d"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\floating_action_button.dart", "hash": "b32917544d6176139cfa3618ca522b14"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\memory_allocations.dart", "hash": "ce79f0f4b543a5fc0d4137792329e870"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\over_scroll_mode.dart", "hash": "a15bcceaecf26e357e99a057299c337a"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\characters-1.4.0\\LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\ordered_set-8.0.0\\lib\\mapping_ordered_set.dart", "hash": "97834a30fbcea1e530fdf8dc97299413"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\devtools\\dev_tools_service.dart", "hash": "9107ebac20b1ea0f4eefdde219bb7000"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\rendering\\decorator.dart", "hash": "4bc71718afd11378e658d06ffb557d4e"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\icon_button_theme.dart", "hash": "eaa572d06b6edfa39d1b51f873c658be"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_wu.dart", "hash": "c0da8171c63f0ab4e822dd094fc2c595"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\meta-1.16.0\\lib\\meta_meta.dart", "hash": "0cf5ebf6593fabf6bb7dfb9d82db735b"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\navigation_bar.dart", "hash": "8d7eed34bbeebcd7bddc0284a516f91c"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\vector2.dart", "hash": "923b6c07cf5e055ab05a38bb666cecd8"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer.dart", "hash": "db799bf48af97b7c0edc93ad96b4a6da"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_android-4.7.0\\lib\\src\\android_webkit_constants.dart", "hash": "ee40e7f976692eb6e581993b0795609e"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\keyboard_key.g.dart", "hash": "52d3612bdffadcf247e4570767678989"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\web_resource_request.dart", "hash": "50a76a0eb65096e74dda3f418407ce75"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_file.dart", "hash": "58edba46526a108c44da7a0d3ef3a6aa"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\text_selection_theme.dart", "hash": "bd315d8c5aa8b4a998458cb3e8068b0c"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\rotate_around_effect.dart", "hash": "39eac0475c964c2763a46c6c91118fd3"}, {"path": "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z", "hash": "93b19c321840bf1c0f68fde367021dff"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scroll_metrics.dart", "hash": "e11d89b7d2933ba28814915b300af866"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\orientation_builder.dart", "hash": "525ce10fb97ccc6f3407f8e962043ca5"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\inherited_notifier.dart", "hash": "93219dc70f767a24408583015695d38d"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\grapheme_clusters\\breaks.dart", "hash": "73189b511058625710f6e09c425c4278"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\components.dart", "hash": "bc1ffccf082e2fcae728d817248c5d39"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\functions.dart", "hash": "41f7bdb7d1eb3c86c21489902221b859"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\automatic_keep_alive.dart", "hash": "c33e24b9037c1e3435006428541f2685"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\linear_effect_controller.dart", "hash": "882411971aa534e4eac48c5438216f73"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\size.dart", "hash": "ea666961619fb7431c8acea9c24f2095"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\leak_tracker_testing-3.0.1\\LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\circle_avatar.dart", "hash": "72ede1aea65e26bd0e0b44da56f41473"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\button_bar.dart", "hash": "5963af31f4a3213cf4ce04d2b1ca7eb2"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\platform.dart", "hash": "235a9cbd9dfd2869ed39062656c7e07b"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\pointer_move_event.dart", "hash": "3c42bc26181014ae7d2d111a4020c440"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\score\\score.dart", "hash": "58b9bc8a40fd3e2f7d9d380d0c2d420f"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\menu_style.dart", "hash": "ba859da84d2120e70ef3404c552ab425"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\standard_component_type.dart", "hash": "702ebe43a77fbc5d1e1ea457e0bce223"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\sprintf-7.0.0\\LICENSE", "hash": "f26476a70de962928321bf9e80f9029e"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\decoration_image.dart", "hash": "bc264448c0fc92fbe61a1cfa17b42d0b"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\slider_theme.dart", "hash": "8c6db6c750bdcb03ce0f7e6d89423546"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\quad.dart", "hash": "9a043d96e7ae40786de66219219bea4a"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\has_game_ref.dart", "hash": "b218b55e12c52087e7833415f681253a"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\fragment_shader.dart", "hash": "5f7e17e1506903e7508b6b32556c3906"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\ellipsis_search.g.dart", "hash": "13bceb5508fcefacc9ed46137d43844e"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\lib\\shared_preferences_foundation.dart", "hash": "b72ebe27944e3a75601e56579bb92907"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\platform_channel.dart", "hash": "62f20a61fcca0d9000827ee5e54a30f2"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_wkwebview-3.22.0\\lib\\webview_flutter_wkwebview.dart", "hash": "0481dd07193dbcffcccf75393161ad27"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\flame.dart", "hash": "ff3b44ff923db12b6db36e59bff90f0d"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\geometry.dart", "hash": "4e4d2071604e3c34d057490624ed1c98"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\image.dart", "hash": "9cab1eb2dd33f2254bb914336cd92759"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\framework.dart", "hash": "8b2a5e0028524c90fb341f103369e087"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\elements\\group_element.dart", "hash": "3b9570296dadf2336a73b5703c56aa5f"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\inherited_model.dart", "hash": "39d8ca399102782c09b864fa92d51f3c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\process_text.dart", "hash": "53c1ff11b2873a67a9e4c0277a31a856"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\sliver_multi_box_adaptor.dart", "hash": "********************************"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\hitboxes\\shape_hitbox.dart", "hash": "********************************"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\decoration.dart", "hash": "6da099b31166c133a52bfa3ab7a1b826"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\button_theme.dart", "hash": "b74031010357e2c6c8e502ed635f6d87"}, {"path": "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\lib\\data\\models\\game_fact.dart", "hash": "31cf31f84dd8fd0e18227cbb326d921e"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\ink_sparkle.dart", "hash": "ffd5fbadea48a2028f714bb6a918cb45"}, {"path": "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "hash": "518c8986a5ca84c37b0772d6421efaf5"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\pause_effect_controller.dart", "hash": "d42c8e133bdde804c90468983ca96a33"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\behaviors\\follow_behavior.dart", "hash": "6cdbc3471dd8ced8302cfe41dea711d9"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\matrix_utils.dart", "hash": "451a797396c48722517ac4fca1ec1c62"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\broadphase\\broadphase.dart", "hash": "8520b187e3ca4ed86d26dd8e0655311c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\badge.dart", "hash": "1d183dd2fa481536757ae61cc77ed9da"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\infinite_effect_controller.dart", "hash": "2f719cd06c3ce3009a50742b0c3d68cd"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\hitboxes\\composite_hitbox.dart", "hash": "********************************"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences-2.5.3\\lib\\src\\shared_preferences_devtools_extension_data.dart", "hash": "3f47c1f73c7a4541f98163b83d056456"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\scaling_particle.dart", "hash": "37db9f159f3b3c426b2380bd3c248a28"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\reorderable_list.dart", "hash": "64a99cea669ce795e61ff3aed5eb0de8"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\move_along_path_effect.dart", "hash": "dd2b67744f740d658bd8814a52044b1e"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_wkwebview-3.22.0\\lib\\src\\webkit_webview_platform.dart", "hash": "1c8bef4805260af7ab98b7587e52addc"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\xdg_directories-1.1.0\\lib\\xdg_directories.dart", "hash": "737107f1a98a5ff745dd4e3236c5bb7b"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\basic.dart", "hash": "a263371650b0cd7ecdf0c5b3949b31a3"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\ink_ripple.dart", "hash": "3f591c8127c07a81900c2b23dc82a909"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\data_table_source.dart", "hash": "bdd3a31817dfc052c506f3a7e2556fcb"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\plugin_platform_interface-2.1.8\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\provider.dart", "hash": "08fb5f27432143c416f473db763fa8c6"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\bottom_navigation_bar_theme.dart", "hash": "b7856aab58a26c2a46a4735f1d2ed4b7"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\proxy_sliver.dart", "hash": "3d6895a1ee4fa4ad4b5c4dee67bb38cd"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\flutter_logo.dart", "hash": "494881558ae50a1d5273ceb66b43ed85"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\cache\\matrix_pool.dart", "hash": "790a89cdf04a92e9ec39f69adb20c43a"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\material_color_utilities.dart", "hash": "11df661a909009a918e6eec82d13e3ff"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\button_bar_theme.dart", "hash": "462f8fa010cd5b4b90e61714d27a9954"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\mouse_tracking.dart", "hash": "ecb57271d69343712d88a50442f2e8ed"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\provider.dart", "hash": "7c0851720900806fa2a397a81c81875e"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\sheet.dart", "hash": "febef97c3e452a11afff18c7b4eedad7"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\shape_decoration.dart", "hash": "e4523a14b383a509595ff9501c8cedf3"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\has_ancestor.dart", "hash": "965dbdb33247b41a567473e49be12790"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\rendering.dart", "hash": "48fb786b2f335988d402e1dd19a2c275"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\iterable_zip.dart", "hash": "df699735e3bcd730f16ce377d562f787"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\image.dart", "hash": "b06018282363c4cfcf6d9693d15b29a2"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\characters.dart", "hash": "99b4d15f76889687c07a41b43911cc39"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\time.dart", "hash": "606636e865b06ca9bbefe3560a32fc7b"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\tab_scaffold.dart", "hash": "220eb17aa14783375f802e8d5cf5c49b"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\keyboard_maps.g.dart", "hash": "feaa27101434fc1590c19d42ec8b407f"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\text_formatter.dart", "hash": "6ebce6d2b9c6488571c530cdd3c0a723"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\debug.dart", "hash": "36b7c748941e821dea3c3c964032ac12"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\inline_span.dart", "hash": "716cd061dfa876531364acffce466bc5"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\paint_particle.dart", "hash": "084aa859cb7ab187da41d1db3b6b5d24"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\ffi-2.1.4\\lib\\src\\arena.dart", "hash": "04f3f5a6ad35c823aef3b3033dc66c3c"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_directory.dart", "hash": "62da8696885bd25977675ac4f7f1aef9"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\binding.dart", "hash": "73e482e3ef72695bcdaaf4888ca76868"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\opengl.dart", "hash": "21baec3598b81f16065716b8ee97c8bb"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\platform_views.dart", "hash": "aeac5f6dbff3475f74a377686e285d6b"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\system_navigator.dart", "hash": "6a7f49ff645804c67a62656a0046ca5d"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\sprite_group_component.dart", "hash": "690abbf5fdb6d8ad9ebb1273a5e60541"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\layout_builder.dart", "hash": "28c62a3d01773f3a10cdd12523fe7a96"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\cam16.dart", "hash": "ca959e5242b0f3616ee4b630b9866a51"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_close.g.dart", "hash": "9c7196e67b951143d548d72aaa0e75ec"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\scroll_position_change.dart", "hash": "56026b6e71a025773b8b6236d7708d92"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_windows-2.3.0\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\notification_listener.dart", "hash": "ffe5ffa81a95dfa29637da7f638fffbe"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\common.dart", "hash": "d7eafa3cf0f7e30ce2e5c01279154b40"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\no_splash.dart", "hash": "363dc40bd108240cb513878e107a260d"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\service_extensions.dart", "hash": "f45299fdabda0bd3b2ed77068c7d1de6"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\bottom_app_bar.dart", "hash": "35e08f69f693f32fd66c73e92bbeac3d"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\internal_style.dart", "hash": "974d0c452808a1c68d61285d0bd16b28"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\palettes\\core_palette.dart", "hash": "d35b72b249d19f54a4cd6f22ff3299e9"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\webview_flutter_platform_interface.dart", "hash": "0503ce57073fbbfc3fa9a0139f3e5d97"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\slider.dart", "hash": "f0fdc3dcbed6a67203aefc93ab718392"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\input\\keyboard_listener_component.dart", "hash": "c237c5001f95a58a3c2fd80ef6bf48e3"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\material_button.dart", "hash": "61e660e12e1c2bd4b570416482c8f18f"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\directory.dart", "hash": "8f4de032f1e2670ca51ce330a4de91a3"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\widget_inspector.dart", "hash": "b4fee047182e4490f2e42cf2aa289637"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\queue_list.dart", "hash": "02139a0e85c6b42bceaf3377d2aee3de"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\listenable_provider.dart", "hash": "fe16b487322631b50c3cbb09de987315"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\cupertino.dart", "hash": "d0d99e4aed47c375c97c9822cfdaaa28"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\dropdown_menu.dart", "hash": "615c74b96a89de42a5207de4daf66eae"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\position_component.dart", "hash": "c4a199118e19934787b86b4d3638d1bd"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_android-4.7.0\\lib\\src\\platform_views_service_proxy.dart", "hash": "139320157f06a8d21dbdffd5486c61e3"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\router\\route.dart", "hash": "a93e4755c7bb975ecc94f5b63fc18192"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\tab_bar_theme.dart", "hash": "19ca41c14be0e05637a511d945b2f810"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\localizations.dart", "hash": "aec52d42480a7d3a5be9e7eae6370ebe"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\consolidate_response.dart", "hash": "d773ee48068ac90b654336d1ec93541e"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\animation\\listener_helpers.dart", "hash": "de392e4c7043eeb731474c52267542d5"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\heroes.dart", "hash": "37114dfadd8f365fa911320571efe5a5"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\tween_animation_builder.dart", "hash": "31fac5b5882f19d242724b858b97d228"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\print.dart", "hash": "d073924ebcc169bd1a47c3f695925478"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\url_change.dart", "hash": "111d09c1c4c7e2aa16e664f0bd4e5322"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\wrappers.dart", "hash": "21e56afda1f096f0425a34987708ed56"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\navigation_rail_theme.dart", "hash": "77c6bf950eb7c87baf89b95368f6acc6"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\text_box_component.dart", "hash": "********************************"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\zigzag_effect_controller.dart", "hash": "ca39ccc5362f6e9d5c9121832635e3c3"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\LICENSE", "hash": "22aea0b7487320a5aeef22c3f2dfc977"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\characters_impl.dart", "hash": "6297da5be01fb7c0d5c4aaffe7a27a50"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\equality_set.dart", "hash": "4b5d82ddeb09bc46ae0e980616ce0109"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\triangle.dart", "hash": "7d2bdb4801fc8b3a110f36d5e5fa59f5"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\box_border.dart", "hash": "********************************"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\equality_map.dart", "hash": "700328ab0177ddfd9a003a8c15619c1a"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\physics.dart", "hash": "ffd7e9991334466f08df7afe0c721048"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\lsq_solver.dart", "hash": "d7c562d566e2e21539ea8288f21e7876"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\placeholder.dart", "hash": "f25ad5ad3704aecbea8e55cdf889b1b0"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\gesture_settings.dart", "hash": "ce7c719e306ad9ab02b19aafbd70178c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\_web_image_io.dart", "hash": "7f7fc8274f08decca0738d6873b9b8fa"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\draggable_scrollable_sheet.dart", "hash": "840f928248b5e234ff1fbf4ea6b27b6d"}, {"path": "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "hash": "dc3d03800ccca4601324923c0b1d6d57"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\column_node.dart", "hash": "46bc59f9ab6355e473864342e0eb9783"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\input_border.dart", "hash": "dab909dedbbf46bb14d7a26091ac10b7"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\styles\\overflow.dart", "hash": "8d310bf756d8cb62bad5d1c58bd6e08c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\menu_theme.dart", "hash": "0908983136fa6ff9c6aa302e79ee7bb6"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\broadphase\\sweep\\sweep.dart", "hash": "ba4e119c99fe36ca705fc0dbcfafdea3"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\transitions.dart", "hash": "d36e7d651314b6c9bb80b1616ae188b2"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\vector3.dart", "hash": "a1e740a70209acedc9ba1bff7141c14c"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\cache\\images.dart", "hash": "3f593378282d2932e5c2ded83b390372"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\bottom_sheet.dart", "hash": "021f3c262c5cd50dc0ad2764995358c6"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\utils.dart", "hash": "8a7e3b181572ed50e923e5dc05a7533d"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\plain_text_node.dart", "hash": "cc6957f15f182b249f3985c9b7f531df"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\scheduler.dart", "hash": "3ac176a9235973980af3b75bd0c237ff"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\spawn_component.dart", "hash": "dde4ac726fbcad1c01ad75dd25d46134"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix2.dart", "hash": "7f164e577cfcf8c8295947195cde2a7c"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\grapheme_clusters\\constants.dart", "hash": "0672d853d5097a03eddc7dbe558eeabd"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\container.dart", "hash": "de961c25d71b7a769062627541bfbcbd"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_file_system.dart", "hash": "06c73ad137e5db31d7e6ba4258ac13c7"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\input_chip.dart", "hash": "92c59c394e56bfb4a3d764c714a45c1c"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\navigation_decision.dart", "hash": "6f3bbbb0aa2a6e5ac518d26192918a12"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\rotated_box.dart", "hash": "********************************"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\annotated_region.dart", "hash": "a05b35e402965727a6b38bfc20b6ea15"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\paginated_data_table.dart", "hash": "dc8584d821b4ba6c384e7fae602c6808"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\drag_end_event.dart", "hash": "e60f25d225c84eeacffc83a070e6edcc"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\dialog.dart", "hash": "68b4adec8e855b7c35d53c486652f424"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\semantics_debugger.dart", "hash": "bc19869e91597ad295ed0aa82807d433"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\two_dimensional_scroll_view.dart", "hash": "4ab023737ac892222c19c6a32b176f9e"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_file_system_entity.dart", "hash": "22f170a8dc9abfac2942555e83589e1a"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\viewports\\max_viewport.dart", "hash": "11582bb62e1b45c0fa9afacd5be97353"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\sprite.dart", "hash": "94796ca22b2309bef33a43d5013a5ca0"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\curved_particle.dart", "hash": "37d3e64cbb1499e5cc320512863190e6"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\alignment.dart", "hash": "d2e982be997d037d953557167bff1e53"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\async_provider.dart", "hash": "3a2d20718f772fbb710aec7dc5e0bf80"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\undo_history.dart", "hash": "e7ca145d8d308187bc0127bd941b1759"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\pause_play.g.dart", "hash": "51069c14005cc63df73f7c8db5520f31"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\animation\\animations.dart", "hash": "0d91c8c6ebb2c8abf67598e76180c4f5"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\vector4.dart", "hash": "7d33539b36e15268e2f05b15a9f5e887"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\search_anchor.dart", "hash": "9ed92432ec854ecbc9df6407d886463d"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\math\\tmp_vector2.dart", "hash": "ace06b8e928d64908837f75d08f09cbc"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\devtools\\connectors\\position_component_attributes_connector.dart", "hash": "b8757f3b8f6bf42356b255b9d347af6f"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\interfaces\\multi_drag_listener.dart", "hash": "cf9030deb4ff28afd76c9c3eb4ff6acd"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\utils\\math_utils.dart", "hash": "e4ee21048ab83cc50d61ac3784afa9f5"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\text_boundary.dart", "hash": "8ebfd4103b3ffc0ad516a44a2ee40748"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\texture.dart", "hash": "238464b246c0e0f60bc0fa9240909833"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\input.dart", "hash": "d60202db4eb466d4e01974599fcc8226"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\list_view.g.dart", "hash": "63701253bb9a85c00b68c2d51358bad6"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\x509_certificate.dart", "hash": "f234000ceb9d457fa80993e30f7aa769"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\meta-1.16.0\\LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\nine_tile_box.dart", "hash": "********************************"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter-4.13.0\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\vector.dart", "hash": "7ba48caa7a6a4eac8330274dae899e48"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\move_to_effect.dart", "hash": "e161efab0f7fedd691dc5abc4f6b5071"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\image_resolution.dart", "hash": "df6b7325835a6d1da457ca5056ab1860"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\path_provider_windows_real.dart", "hash": "43f4676f21ce5a48daf4878201eb46bb"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\painting.dart", "hash": "e6c5d07cbc53020acc5c08062c8b57af"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\shadows.dart", "hash": "dafc76ada4dc3b9bc9a365a7da786797"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_wsmeans.dart", "hash": "6c6dfd5ba4546c1f32201555d6cff215"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\proxy_box.dart", "hash": "********************************"}, {"path": "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\.dart_tool\\flutter_build\\0c8992056cfdfeb44696f2a8efa5014a\\dart_build_result.json", "hash": "1f8e8e6dbc6166b50ef81df96e58f812"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\multidrag.dart", "hash": "e816d9ff65a2c6401329b0df85fa47c7"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\binary_messenger.dart", "hash": "57699e53ee54d843153d1ef4dd086d64"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\common\\line_metrics.dart", "hash": "d4bda996c7485da2c3ed9ed25d5931f7"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\flame_game_mixins\\double_tap_dispatcher.dart", "hash": "1315cc0eb14d01f65cd0718c0c36fac4"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\renderers\\text_renderer.dart", "hash": "585b878196e2661d4826d4fa6f67a8b1"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\view_list.g.dart", "hash": "97fc1400dd55cb4fceecb33260f0f978"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\checkbox.dart", "hash": "********************************"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\sliver_fixed_extent_list.dart", "hash": "7eb989577e5ba101feca9c465b25264b"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\shortcuts.dart", "hash": "201255868d9b2a9a29f0dd4b846bf410"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scroll_physics.dart", "hash": "939bb15a6ae926447a3f2e4c0f3a5d39"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\interface_level.dart", "hash": "97fa80600d57e253a846a881c4aaae73"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\javascript_mode.dart", "hash": "1ffba8301a3aae3f81db122d65fb1333"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons.dart", "hash": "d8a7b96e6465831f5cf6de56ccddf6b4"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\common\\glyph.dart", "hash": "f3e27b783c691ab4f3c56bc3451d0dea"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\tagged_component.dart", "hash": "e454e395ab8441422ea4e1a2f05857c3"}, {"path": "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\lib\\ui\\screens\\main_menu_screen.dart", "hash": "cf3d38b767feb1aa9e2bf504d46b9a72"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\audioplayers_darwin-6.3.0\\LICENSE", "hash": "c40600261a3b45d01ebc98bcb0a6b2d5"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\raw_keyboard_listener.dart", "hash": "5588e04d2462fa3c03dc939826b0c8d1"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\icon_button.dart", "hash": "63bfda24ff899d90b8cf0de45c1c5c55"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\semantics\\semantics.dart", "hash": "6bc38d604adab605e07dadaa4f8c3319"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\text_form_field_row.dart", "hash": "fb7f2cf0e21cde2881330bd17eedfd4f"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\measurable_effect.dart", "hash": "70d07f1c21905ebc06a22885621351bb"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_android-4.7.0\\lib\\src\\android_proxy.dart", "hash": "dbdb9268dd85906278f7c329a9b61625"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\duration_effect_controller.dart", "hash": "047d79a43bb73f861de03032f50da192"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\color_effect.dart", "hash": "3a7c050bbc0576857fdf5f4213b3d5f3"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\radio_theme.dart", "hash": "6383ecc859333da15aaf986d9897a7d6"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\broadphase\\quadtree\\quadtree.dart", "hash": "1f9467847754ba511f2aa0b12ba755c6"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\lib\\src\\shared_preferences_foundation.dart", "hash": "db8ef5ac4d806e72f7b356056cb50b1f"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local.dart", "hash": "22b26473ffd350c0df39ffb8e1a4ba86"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\lib\\src\\messages.g.dart", "hash": "d8a6ceefc2ed13b75c503d01c8911fd6"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_neutral.dart", "hash": "3ee18da390e16ca65f2ef168adb8a1ef"}, {"path": "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\lib\\ui\\screens\\game_screen.dart", "hash": "6bf9726f9c4c6a41a5992ef045b4517d"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\timeline.dart", "hash": "ff300e7139d4b384c9ba4c12a100f68b"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\progress_indicator.dart", "hash": "9b89a526ec4c8a6c46b3a4bcf3cca0b5"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\glow_effect.dart", "hash": "12b4ee5d1b0d6284fbe9a5ef9a46141e"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\app.dart", "hash": "04ebb81cbcdd308bf43cb79bf12631e2"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\message_codec.dart", "hash": "16cc4a3c97bbfb29764163f9783954cf"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\rendering.dart", "hash": "31b6e401282dccfbeae67ee82469cbf6"}, {"path": "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets\\data\\facts.json", "hash": "5834c4cd4b6e3b9b215c87dde16d0413"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\provider_interfaces.dart", "hash": "bbbed917216e2778db5d7895a8c572f0"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\platform_view.dart", "hash": "58024a76590b0e38955acf1b045a1a00"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_celebi.dart", "hash": "f12f9a9b8bb504f4617bfd1c00d403f0"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\icon_theme.dart", "hash": "c5df707bc55d571491bbfe69ab9e3329"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\raw_keyboard_web.dart", "hash": "2c907a9b2d913f98aea742b5ead8d39c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\app.dart", "hash": "c03e0a490116f5c2494671b818253be6"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\converter.dart", "hash": "24dd1f01d0ce298347e47fd60702007c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\expand_icon.dart", "hash": "808beacf0746544002dd1206a6c7d387"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\list.dart", "hash": "2e73eead8723b8dca94fa08122e23193"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\sliver_resizing_header.dart", "hash": "02b2fe6a8dc8ea38fa70193518928cbc"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\utils.dart", "hash": "fe2489ea57393e2508d17e99b05f9c99"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\double.dart", "hash": "eea29a48a1668304fc1c0e119f4ff264"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\diagnostics.dart", "hash": "d29e9119669959c9cc3eba800cc79d90"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\cupertino_icons-1.0.8\\LICENSE", "hash": "2d0c70561d7f1d35b4ccc7df9158beed"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\remove_effect.dart", "hash": "61e23252c994805011c0008f503c2854"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\raw_keyboard.dart", "hash": "fa98c8c6d036ea07d9ee49ea7d30f25c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\error.dart", "hash": "60f88878959c8f38a1f8cf481bc3d76a"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\play_pause.g.dart", "hash": "4326580ee93b3a584477cc185146eb2f"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\audioplayers-6.5.0\\LICENSE", "hash": "c40600261a3b45d01ebc98bcb0a6b2d5"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\colors.dart", "hash": "243ee4a9d79acc2c478caf01088decfb"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\sprite_sheet.dart", "hash": "e1a5b4ebcea3c72e1c0dd67e658bac2c"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\folders.dart", "hash": "4bd805daf5d0a52cb80a5ff67f37d1fd"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\text_form_field.dart", "hash": "fbe26611bcfe5bde97092ae95ba95eec"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\value_listenable_builder.dart", "hash": "ec3a5f7a8288542858278a8702578709"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\platform_view.dart", "hash": "3e386920be87d5a0e9835148593dffe5"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\expansion_panel.dart", "hash": "7a67893da4c81183e350cc0ecc25667b"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\layout_helper.dart", "hash": "ff0c28954cbb930913ed52a41f11189a"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\viewport.dart", "hash": "fc306b1d4924375bedfa4c52a06053af"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\gesture_detector.dart", "hash": "68d77490fd54976fbcdc4cd48e97fd7d"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\licenses.dart", "hash": "43be915d6729f5b59a5dc76fd923b31b"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\core\\component_tree_root.dart", "hash": "f5854202704bf06c03f186850736adb9"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\bottom_sheet_theme.dart", "hash": "a400dfa676904fa8a84c48146ff8e554"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding.dart", "hash": "328ff975234df68963cb19db907493ff"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\adapter.dart", "hash": "ffeb03ba26ab6fd8a8c92231f4a64bec"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\thumb_painter.dart", "hash": "007e3dfc7780af8def8a39f3be6e0ebb"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\block_node.dart", "hash": "d3b8b304783e8b3da5f96d8d023feb68"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\date_picker.dart", "hash": "cb6abede12366aefeffc99d8f9c4c885"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\lib\\src\\messages_async.g.dart", "hash": "2bd174cad1b04e4cca9ba7ac37905e5d"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\physics\\utils.dart", "hash": "670717573525d5e0518a4063c6fd9231"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\beveled_rectangle_border.dart", "hash": "a4474d2d39dd9373580879eb4bd18ab7"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\color.dart", "hash": "511b0aea49dbf5cf6a341ff88eeb048c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\dialog.dart", "hash": "3f407711541a40aa7584973e8f8dc03b"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\tap_and_drag.dart", "hash": "f1be26fd7d1129f7c28d979d72027718"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\scheduler\\priority.dart", "hash": "9051680cd2078f92c9c56831272643d5"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\table.dart", "hash": "d8563d6c5693445540a807bb73dff06f"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\experimental\\raycast_result.dart", "hash": "33281d5cd4971ec352af14b32ef7f331"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\data_table.dart", "hash": "2d2665cc38035cce3d915b6893800623"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scroll_view.dart", "hash": "02bf5117b8c6def8ea98ff663a374da9"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\offset.dart", "hash": "4720e05336d444932d80d617029dec27"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\gesture_hitboxes.dart", "hash": "********************************"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\moving_particle.dart", "hash": "62a8e7fb8575e4e3261811143fdca978"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\constants.dart", "hash": "9628979f9e240d20f992073c54489890"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\sprite_animation_particle.dart", "hash": "d6aeb7146c0c01b87e3a2d155b7bf62b"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\page_transitions_theme.dart", "hash": "907abd90f09ee08374f744c4cebc6791"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\javascript_dialog_request.dart", "hash": "a8d360ec037224d19465bed3ff3c7b7b"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\data_table_theme.dart", "hash": "ada88e231d33ef7c2855cecc98e8c6a2"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\divider.dart", "hash": "7d5370ce28d67f1901da31171aebf7e1"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\app_bar.dart", "hash": "fb42af8514d87dbb35b1d9ad465896f2"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\position_event.dart", "hash": "4ad0251a700dd33115921a936f5a1c5a"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\colors.dart", "hash": "9cd03844c4e859875c10c9708556a0db"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\file.dart", "hash": "1026f587763defb6fb1eec88c2154a3d"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\fps_component.dart", "hash": "afb567d0de66d55ee1592b94bd1d063b"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\game_mixins\\multi_touch_drag_detector.dart", "hash": "6f251b30d84567f8ae78ffb5bc47ed2d"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_content.dart", "hash": "78e53d9a4963c0d19c5ea355a0946e5d"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scroll_context.dart", "hash": "a2701656bb3160ea810ab576c50cbd65"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\app.dart", "hash": "2309d30caa73271cde990976da967809"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\utils\\color_utils.dart", "hash": "0938e0447f447ceb7d16477a0213ce2c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\keyboard_listener.dart", "hash": "c87e92035314a4d3e52faf886355d0a9"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\audioplayers_web-5.1.1\\LICENSE", "hash": "c40600261a3b45d01ebc98bcb0a6b2d5"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\reverse_curved_effect_controller.dart", "hash": "d52ba127db5936170f81a11541088769"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\toggleable.dart", "hash": "8120214a606fbc8c98cfff2b27eca1cd"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\arrow_menu.g.dart", "hash": "9621a4c337d24a926ff50126ce88d7fe"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\double_tap_event.dart", "hash": "6da60adb9e6ec33903f8f5c0dde6336c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\spell_check.dart", "hash": "16b7d416940fc53eca969b344627767f"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\physics\\gravity_simulation.dart", "hash": "e39c804b77ec1bf1f6e4d77e362192c1"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\switch.dart", "hash": "a39ee9973bb91bf7fbbf9501861510be"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\color_filter.dart", "hash": "29426d64d0c201a4d7e7492434b13463"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\geometry\\polygon_ray_intersection.dart", "hash": "36167957e86250e376a634bce2b4167a"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\sliver_fill.dart", "hash": "1fbfdb508cbaf318a89890747d632a67"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\raw_keyboard_android.dart", "hash": "249b2817c1c912b9942e8acc7987adb0"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\viewports\\circular_viewport.dart", "hash": "11fb070c335b4b57c9877bdbf07e8f4f"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\debug.dart", "hash": "49bd72b2f6d097b43a24ecd799f3d75d"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\layout\\align_component.dart", "hash": "e64a90fe4fc1c89b638332f84da58653"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\paint_utilities.dart", "hash": "9fda069b501810ac8093f3c0ad80e3f4"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_map.dart", "hash": "13c9680b76d03cbd8c23463259d8deb1"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\geometry\\line.dart", "hash": "19c9fba0e61d29c5cf8b14e4259e31c2"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\stepper.dart", "hash": "632055fb4cb7c96929a29b8ee8a887af"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\viewport_offset.dart", "hash": "f3ce35c15285bb22d0a813b27365491a"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\aabb2.dart", "hash": "f8fb1733ad7ae37b3d994f6f94750146"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\elements\\text_element.dart", "hash": "f48329a705d81c3a680a32aa3762825b"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\platform_selectable_region_context_menu.dart", "hash": "5b1c50bdd6d4c64f4c6bf4cba82d48e4"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\lib\\messages.g.dart", "hash": "3f45d05cfb9a45bf524af2fa9e8fb6e6"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\fixnum-1.1.1\\LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\elements\\group_text_element.dart", "hash": "8d7b92743872a99fb81d7e9043ed0e95"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\sphere.dart", "hash": "d1089412c69c2ca9e4eeb1607cf0e96e"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\rendering\\rotate3d_decorator.dart", "hash": "5c1ece15d6f3ed98208e068739cff35f"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\mergeable_material.dart", "hash": "262d4e04ee86cda8a7853dd7cca0584d"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\experimental\\column_component.dart", "hash": "1ef34f240526b9c20f5fc6f8ea3f2eab"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\hitboxes\\polygon_hitbox.dart", "hash": "********************************"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\actions.dart", "hash": "b438b92d95aa74f212a0c40b725e10ba"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\widgets.dart", "hash": "85ba1a3bc73d7ffd54faea23f6c727fb"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\close_menu.g.dart", "hash": "97f8d480ec6ac1778506d29f498a1e6c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\sliver_layout_builder.dart", "hash": "6c0b758a4a7b8966ccbd64932d1ceefc"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\two_dimensional_viewport.dart", "hash": "6f4d72d38afe16a9eeb2d21bc14e2460"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\speed_effect_controller.dart", "hash": "cab4592de2266a62f00b9f1299d6ee7c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\strut_style.dart", "hash": "1d7927056badbebd8f79686e3ee05ffc"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\editable.dart", "hash": "8939744fc50834903aba130d3b18765f"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\curves.dart", "hash": "2fac118c3201a4bac960d7430ddda0c6"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\particle_system_component.dart", "hash": "19d5a8c4bcd320d0ad03c74642b3ab6a"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\node.dart", "hash": "eb8af6a9d85f134dd7d7c1ae2dd07e29"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_file_system.dart", "hash": "c23a0415bdaf55efdf69ac495da2aa9b"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\slotted_render_object_widget.dart", "hash": "2babfd7c893533e7f7b3d1edcc075b64"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\sprite_component.dart", "hash": "0fd054cba0185a2b3b12d291c2100c98"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\ordered_set-8.0.0\\lib\\queryable_ordered_set_impl.dart", "hash": "85c215e5c6b9d5d753837a352c2112d5"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\unicode.dart", "hash": "c692323b8d9e3ed3c4c134ba07ab94e6"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\platform-3.1.6\\lib\\src\\interface\\local_platform.dart", "hash": "9cc2170ec43e47681be6cb2a313ba1b5"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\callback_controller.dart", "hash": "1625fc1ee90fc0e9349c66cba117266f"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\text_theme.dart", "hash": "da23e9a23f3c0bd8f03adb30165f3f2d"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface.dart", "hash": "5145b27b3db429f9f1da26cfe563bd02"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\bold_text_node.dart", "hash": "c584962a800a9818b781e40623db70ea"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\context_menu_button_item.dart", "hash": "15308b82e6b7d5df3058f8e769084a67"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\experimental\\geometry\\shapes\\circle.dart", "hash": "786f9ba7ed7f451229ded2ca357c4299"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\input\\hud_margin_component.dart", "hash": "5daa99a375876bf7c6ef20932aa421ad"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\binding.dart", "hash": "df77eb750191b494da865a219383e691"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\ordered_set-8.0.0\\lib\\ordered_set_iterator.dart", "hash": "487adf0ce417119cd94990739da57627"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\card_theme.dart", "hash": "d65982353903b8d6b3d8697fa43abc7b"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector.dart", "hash": "6a67d38bafe568f1b4047286d586fbbc"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\interactive_viewer.dart", "hash": "34d5859bd13d7c941916846f41ef3b31"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\navigation_drawer.dart", "hash": "1f418dd0c28e28b509d8179a39898a5a"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\repeated_effect_controller.dart", "hash": "49fc57692e570b451567fba672e57968"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\input\\advanced_button_component.dart", "hash": "073f64dd0eec9db2cf4323894f6a296e"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\clock-1.1.2\\LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_android-2.2.17\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\move_effect.dart", "hash": "b63d92d31752d7d229770746b21e003c"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\boolean_selector-2.1.2\\LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\basic_types.dart", "hash": "00f26750b380e279fd67c68c79734999"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\webview_platform.dart", "hash": "477831678f2617c762d84bebf5256f3f"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\file.dart", "hash": "51ffa7b452686eecd94ed080a1da4275"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_android-4.7.0\\lib\\src\\android_webview_controller.dart", "hash": "051706df6d45bd98abff4b535bcb044f"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\tab_controller.dart", "hash": "90d9ef8c98ba928aace1e5b7099c0844"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\text_editing_intents.dart", "hash": "a0836fe16e69a1daeee421a5dd73f761"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\mixins\\single_game_instance.dart", "hash": "4f6282a2923643aa2780ce605f9fba31"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\double_tap_down_event.dart", "hash": "030eb8e9a28e68ee8aa411b42dd33a49"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scroll_position_with_single_context.dart", "hash": "cf39132e96645ad4603d01e2a71f2ba6"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\sphere.dart", "hash": "63473e31f03ea66a38affa41fd783752"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\system_channels.dart", "hash": "0289bdf9300d66bc785375eafd92b219"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\gradient.dart", "hash": "03eb0db4ace470b8834f6330a9473b70"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\drawer.dart", "hash": "d460667f13c0c9c2ddb60902d2d92663"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\animation\\animation.dart", "hash": "73d837564acafc178f5bf73dc50994e0"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\sequence_effect_controller.dart", "hash": "dd2691837ff076ac5a6dac88317520f7"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\hardware_keyboard.dart", "hash": "f8867a31bedba73738dabade00f04fea"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\banner.dart", "hash": "d82db0f248ca051490d30d8b76686730"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\events.dart", "hash": "7f694f69cb60ba144f59ff752805476b"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_wkwebview-3.22.0\\lib\\src\\common\\platform_webview.dart", "hash": "53c61f8e48a6bd983a3e9d8163bd1596"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\platform-3.1.6\\LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\dismissible.dart", "hash": "1723ae3582276265ebad2922945dbeb2"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\sliver_persistent_header.dart", "hash": "2a36080f4719a0b4485bff0582d9894b"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\http_auth_request.dart", "hash": "8b747b3e31a6d6f2304004353211fb31"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\custom_paint.dart", "hash": "a4a8a699953a05873a0565a3f20e60e4"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\algorithms.dart", "hash": "0976264b99a1702a5d74e9acb841b775"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\platform_webview_widget.dart", "hash": "2f8bf8debe62625220a5d437ba42ff64"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\animated_scroll_view.dart", "hash": "a9e575d272fec21ee0baab114ecd62cb"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\lib\\src\\shared_preferences_async_foundation.dart", "hash": "282aeeb78f4a92064354b5fe98161484"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\isolates.dart", "hash": "8631e44e103ca1be44ae10252f3dbacf"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\lib\\src\\messages.g.dart", "hash": "1567572a579e5f2aab31966d4a056855"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\user_accounts_drawer_header.dart", "hash": "905e412b364d027891e90040b270ebd1"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\cupertino_icons-1.0.8\\assets\\CupertinoIcons.ttf", "hash": "b93248a553f9e8bc17f1065929d5934b"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\animated_cross_fade.dart", "hash": "40a04e94b4e68da57cf200df6c661280"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\term_glyph-1.2.2\\LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\hit_test.dart", "hash": "40a8505ec0b7cd7676ab5efb486432bd"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\binding.dart", "hash": "082f7b9db25627e7eefcba106260a079"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\value_listenable_provider.dart", "hash": "bbd255fe46712b372dfe3b99cb340068"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\dynamic_scheme.dart", "hash": "7536ace8732469863c97185648bb15a9"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\page.dart", "hash": "8760dd2e19b843a03e51134f84d82ca8"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\adaptive_text_selection_toolbar.dart", "hash": "2796f86b16cde28eab42232889d082ed"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\navigation_request.dart", "hash": "9b3f56e1159df163cf4f2b13d1048e79"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\src\\tone_delta_pair.dart", "hash": "f5b38c21bf580c89610a8b58c65aae00"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\web_resource_response.dart", "hash": "9df794779fc351159011a56c9d173bc9"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\comparators.dart", "hash": "8ac28b43cbabd2954dafb72dc9a58f01"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scroll_simulation.dart", "hash": "2fe41384c97b732ca4986150ae7a002e"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\radio.dart", "hash": "86cac5e304f32a8cd937f64fee31ec7b"}, {"path": "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\lib\\ui\\screens\\how_to_play_screen.dart", "hash": "6c79246237208b5f77c117776e9fc023"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\dart_plugin_registrant.dart", "hash": "6c49f36c204d9c39ed8344462e4342f9"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\time_picker_theme.dart", "hash": "c2e8ba48563eaf38bd3b50761b60e973"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scroll_notification.dart", "hash": "a5df6e3153b654738dfd5a67c799e6d5"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\renderers\\text_paint.dart", "hash": "39bc7271ec91b40472ab35fdbcb9376c"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\overlay_manager.dart", "hash": "45af40d8e2e9363ba2352d2c9f76102c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\list_tile.dart", "hash": "eafa783f39fb35b9bb8581b697c3ba52"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\debug\\child_counter_component.dart", "hash": "a7d5c8f593f17bfd295a449155288601"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\palette.dart", "hash": "398f007479cf9e7ce15213ace5cbc99a"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\gestures\\detectors.dart", "hash": "ee06a6f38eb6c609261c9fbf8300beb5"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\inline_text_node.dart", "hash": "b67e2ce3113a3457f9a22e6c11090c59"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\nine_tile_box_component.dart", "hash": "********************************"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\tab_indicator.dart", "hash": "8b1f55bdc7ddfffc5a4d677052b1b789"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\debug.dart", "hash": "136b08c4413778ae615af5f45d39ed93"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\semantics\\binding.dart", "hash": "d49f04d746fa2e01e165d7cdef3abf2d"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\list_wheel_scroll_view.dart", "hash": "a86a7c938657004c2ef7f8562389f468"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\platform-3.1.6\\lib\\platform.dart", "hash": "cbf041463d4a85115a79934eafe8e461"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\toggle_buttons_theme.dart", "hash": "f0f22cf747d09b92d955e41b12852d3c"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\quaternion.dart", "hash": "82a52b42ca10c86b0f48afea0cbe9ac7"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\binding.dart", "hash": "e0fceafe591ad5fd70a41190dd121f08"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\bottom_navigation_bar.dart", "hash": "f31cd1126983d313d533c2f530bd1c33"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vm_service-15.0.0\\LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\behaviors\\bounded_position_behavior.dart", "hash": "0cb4c4bd60e729ca31869b3272cbf003"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\debug_overflow_indicator.dart", "hash": "869bbc5dd32224e5adaeea14f49a95c3"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\focus_manager.dart", "hash": "7389265943ae4de96f69b6975797e3b3"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_wkwebview-3.22.0\\lib\\src\\common\\web_kit.g.dart", "hash": "3d38fe8c376c411030e2dad07aad2cc6"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\input\\sprite_button_component.dart", "hash": "7b89570b8959c2e0eba72f8a5bab7ffb"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\lib\\shared_preferences_android.dart", "hash": "30bffdef523e68fbb858483fd4340392"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\location_context_event.dart", "hash": "9b4dfa4e796d14c3a78e772df4045895"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\vector2.dart", "hash": "6b519d909b25ca9d144af7972d689c6f"}, {"path": "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\lib\\utils\\constants.dart", "hash": "da8b48ab9bbd093f6e3240ca84fac26b"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\action_icons_theme.dart", "hash": "cde6a145081704bf14d1097f7ddb58de"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_iterable.dart", "hash": "67d16e841606c4e5355211fe15a2dbfd"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\curved_effect_controller.dart", "hash": "242c0b893da70b7a90f52f7b20add329"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\selectable_text.dart", "hash": "b7fb4ebe93c884f2ed505604fa1db488"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\status_transitions.dart", "hash": "c909abaa7c0b6b52aa22512668ededb0"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\geometry\\shape_component.dart", "hash": "3e91173956cfe4785347511d5edc441e"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_list.dart", "hash": "5b894ae18be3e2442a34288833184ca9"}, {"path": "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "hash": "4ed46ce370a37c49a136430aad05d4f4"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\long_press.dart", "hash": "7e9cc2885777664a4d8a28ceaaf4343a"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\experimental\\row_component.dart", "hash": "120842735e896a9e0492be302257fd66"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\platform_webview_controller.dart", "hash": "7be81e300ad4e6ea17c68526cf3f1684"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons\\animated_icons_data.dart", "hash": "8d5d3ccddf53eafd7a3094278afe8b93"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\drag_details.dart", "hash": "0469ca72d371bc48cf7f0901c0cd1917"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\contrast\\contrast.dart", "hash": "0c9bd1af5747fd55e7488c731ad32dee"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\plane.dart", "hash": "f0c6d5d05fbdc95ab84f1a63894b7be6"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\animation.dart", "hash": "e76c07fd6945a4eadb2aeebf87b643bd"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\animation\\animation_style.dart", "hash": "a3f622bd8417a8f8a1789761a4cb572a"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\input\\toggle_button_component.dart", "hash": "5905fc6731512fea64f1ad3a199bd109"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\io.dart", "hash": "119ed2f372555dcadabe631a960de161"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\timer_component.dart", "hash": "ce9de879f1b989a5bae8b100fe46c393"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\text_editing.dart", "hash": "1be64f7d80a3db5d33ac240131681562"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\bin\\cache\\pkg\\sky_engine\\LICENSE", "hash": "10f2d960c7d6250bbc47fdf5c6875480"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\semantics\\semantics_event.dart", "hash": "851b8e20b77e3b6cc21ad90cdbc1a1f4"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\asset_manifest.dart", "hash": "4538b5225c156e895c5d603660cb1358"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\platform_webview_cookie_manager.dart", "hash": "1255d62f0a7be683fe89db6256ef8610"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\snack_bar.dart", "hash": "cbeff2d69d62fda433fa85b2fa331821"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\border_radius.dart", "hash": "bcad667eb65d62ec08bcb8781cd89942"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\banner.dart", "hash": "4db88ed53fb502c2c73cf2554abaf766"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\selection.dart", "hash": "4171ccc0ef5ccf7a3355589667cc0450"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\theme.dart", "hash": "fbc4e27193ffde95719ac936bc35071b"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\constants.dart", "hash": "310649eda1830de4f669332f93865282"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\flex.dart", "hash": "16bbe2b548ec9e4ece91aa557d09db8f"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\text_style.dart", "hash": "bcbe75353032d77c9b99a24bb590393e"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\LICENSE", "hash": "ca58010597a5732e6aa48c6517ab7daf"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\primary_scroll_controller.dart", "hash": "1d3152d32f76b361fabfc04ad1eb74b4"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\list_section.dart", "hash": "ccf5dc03f13f3e26baef323b1ff68d0b"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\types.dart", "hash": "30d5630a3fc3405d3c7b476c75dcd8d8"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\math\\solve_quadratic.dart", "hash": "433228434885025ac584b6fc6dfe7f00"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\assertions.dart", "hash": "fc17a3958e284d6b8530b650943fdacc"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\filled_button.dart", "hash": "ad1cdc9c1d06d8af94266565ad966f3e"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\text.dart", "hash": "70440771d3aa7fe232691f7ed1700e91"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scroll_controller.dart", "hash": "99712ab0098106c505b424f8542edeca"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\expansion_tile.dart", "hash": "384464b3480155e7652d42b36d8447a8"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\has_time_scale.dart", "hash": "2d096d6d136a2ba53b08b496d57293ab"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\string_scanner-1.4.1\\LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\custom_layout.dart", "hash": "f11954058bc60e8c9403c769a89da56f"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\notifying_vector2.dart", "hash": "9a1009bddcc243a39b6f35dfc5afdd8d"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\snack_bar_theme.dart", "hash": "2d546104cf408c2277ebf73c3a60d051"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\autocomplete.dart", "hash": "feae31fa46eea795709f39bcb7b8d05d"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\parsed_path.dart", "hash": "cb454929d7810d3ee5aa5fc28283d3fd"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\extensions.dart", "hash": "242b8c55f03d69074ee7dbf7f8616c52"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\path_exception.dart", "hash": "b062a8e2dade00779072d1c37846d161"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\custom_painter_component.dart", "hash": "20420653655966f71d27bfa6ca4f797c"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\lib\\shared_preferences_platform_interface.dart", "hash": "59bb1cba1648db956dccb835713d77d8"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_windows-2.4.1\\lib\\shared_preferences_windows.dart", "hash": "2bc47cc0ce47761990162c3f08072016"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\lib\\src\\shared_preferences_android.dart", "hash": "6f05b68df1b893e73008d1831589377c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\scaffold.dart", "hash": "f0092135c4cff7e4933b157a9d09ce9a"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\search_field.dart", "hash": "1360d39c7acbf1ed4425b847c5bccc8a"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\frustum.dart", "hash": "d975e51852aa1802c81c738dcb4c348d"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\sprite_animation_ticker.dart", "hash": "********************************"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\theme.dart", "hash": "ae1aeff6936f7ec3426e0b531f57a9f1"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\lib\\method_channel_shared_preferences.dart", "hash": "513d6195384503beeb7f3750e426f7bb"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\box.dart", "hash": "********************************"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\opengl.dart", "hash": "9e22ead5e19c7b5da6de0678c8c13dca"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\game_loop.dart", "hash": "2d826defe6bbafa18b6751c66280d1f7"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\utilities.dart", "hash": "db8fd891fdcab94313f26c82f3ff2476"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\collection.dart", "hash": "4ba0a4163d73b3df00db62013fb0604e"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\drawer_theme.dart", "hash": "bbb8de6dfd910b8abb564969c260aca0"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\viewing_conditions.dart", "hash": "cb0d5b80330326e301ab4d49952b2f34"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\LICENSE", "hash": "4b6e0d3fc33cec01b05a00345e855174"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\overscroll_indicator.dart", "hash": "8e02b91714073f6161e7be7f9d8160c2"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\performance_overlay.dart", "hash": "4a77eafe460177c2a7183ec127faabff"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\outlined_button_theme.dart", "hash": "8a8f9aeb0001ca5b48ca4ea93a0f4831"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\styles\\flame_text_style.dart", "hash": "d8e6d42a1dfc6fa46616627f85b72279"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\text_component.dart", "hash": "470514b4702f71aedb5fab58ed357129"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\picture.dart", "hash": "d1be617423088f514994fdbb2a6c565b"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\image.dart", "hash": "1c6f4bde2f41b6d50498e3026f24dd1a"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\dynamic_color.dart", "hash": "7ffb6e525c28a185f737e3e6f198f694"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\equality.dart", "hash": "46e577ec532e21029e9cee153d7ca434"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\collisions.dart", "hash": "fdb7730e1033f0a22ee4bbc2371321f8"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\edge_insets.dart", "hash": "8757b92070b761566d91ba1be6b1b76c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\filter_chip.dart", "hash": "7cff3fd6b3f5da25b63942824d72a403"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\spell_check_suggestions_toolbar.dart", "hash": "5c63d2e99643f92534b54d58dbac13b0"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\effect_controller.dart", "hash": "83f2633a5431be67b917e10e2ea73656"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\vector_math.dart", "hash": "703f2b29a9faedbb501bbc2cd99ba7b5"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\page_view.dart", "hash": "b365e48db065f0ba5206dc697fa3807e"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\tap.dart", "hash": "bca784909c10deeb9795f155b7706c92"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\scheduler\\ticker.dart", "hash": "8351b22319b7a91a7b398c3dcccbc3af"}, {"path": "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\.dart_tool\\flutter_build\\0c8992056cfdfeb44696f2a8efa5014a\\native_assets.json", "hash": "f3a664e105b4f792c6c7fe4e4d22c398"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\broadphase\\quadtree\\quad_tree_broadphase.dart", "hash": "643c5e6b404ddb5b8331718f7ae040e8"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\styles\\inline_text_style.dart", "hash": "180811b99d312506fd75d796755a17b0"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\raw_menu_anchor.dart", "hash": "50154334bdb9b9e012f6e1a4c9f2cbea"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\router\\value_route.dart", "hash": "05e9278432233eddf52fe9042039afa4"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\rotating_particle.dart", "hash": "ccef362ced250af573a012a0221117ae"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection_toolbar.dart", "hash": "4574091bd15f30d7976e2cd392814a71"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\navigation_rail.dart", "hash": "0ce04284aaa974663c60ab9f03332ff4"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\game_widget\\game_widget.dart", "hash": "96092f49a5f2544bfc2afcc81e7fc487"}, {"path": "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NativeAssetsManifest.json", "hash": "f3a664e105b4f792c6c7fe4e4d22c398"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\text_selection_toolbar.dart", "hash": "dbdb73fcbd1f5bef4680a288af14888c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\arena.dart", "hash": "527f66bca3f4ace3771d5ffce977c225"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_wkwebview-3.22.0\\lib\\src\\webkit_proxy.dart", "hash": "263a767d64fa8e178f0e8b074bf780c2"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\broadphase\\prospect_pool.dart", "hash": "22cdaef7dcb305fad560e38bf1115bcb"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\browser_context_menu.dart", "hash": "bfd7a00b9fef90dbf3b6d14f6a2f2901"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\icon.dart", "hash": "68c2698d9480a0bf5a6211ab2145716c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\text_editing_delta.dart", "hash": "3da0a4c6b5e4be03fa0e8e2871e98d01"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\search_bar_theme.dart", "hash": "fe53f754a8905eaf7ccb7d3007ec8ab7"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\monodrag.dart", "hash": "6f50583c3f17a5209d18ed90e5c521b9"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\predictive_back_event.dart", "hash": "7a81afed71f41a7b99c7a3d0d1521ae0"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\sliver_grid.dart", "hash": "f10d1c73e161d0807716dee4ef148c99"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\desktop_text_selection_toolbar.dart", "hash": "35d5b2c786045d1aa7428d0b48b2b3b8"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\shared_app_data.dart", "hash": "beb0225376869e0c92a19831596494dd"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_directory.dart", "hash": "18b0559a8cbfb3b3a3d34bbbea4669c7"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\search_ellipsis.g.dart", "hash": "4213cdf8a94244f8d19a59844563dd53"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\radio.dart", "hash": "d943510867769f5dbb0919992d94abf7"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\basic_types.dart", "hash": "861a19ff01e3f58d95d668b9fd4054f7"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\editable_text.dart", "hash": "f0c55410877496bd54ec3144eb434a27"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_android-4.7.0\\LICENSE", "hash": "bf2ce082976b40a77b9be405d83dad59"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\choice_chip.dart", "hash": "1e5182051ef0e48e07643f573e5f4bb0"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\parent_is_a.dart", "hash": "2835948007134eb6521c93e4f76461ef"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\persistent_hash_map.dart", "hash": "b8fb4215060bb74d8025202cabeab63a"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\plugin_platform_interface-2.1.8\\lib\\plugin_platform_interface.dart", "hash": "8e49d86f5f9c801960f1d579ca210eab"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\opacity_effect.dart", "hash": "4fae391eb5c137530b6a0e07fdee0b96"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\synchronous_future.dart", "hash": "fb5592ffbdb3669d56c8d1cb23ed3033"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\core\\component.dart", "hash": "97fe0746ffad44828e096d255ce9afd4"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\foundation.dart", "hash": "f2027810282f79cfd11771b68f8bf50d"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\platform_navigation_delegate.dart", "hash": "ac62a3b87b64443265423b4ba48611f7"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\debug.dart", "hash": "203d46859b027ddaea624814ff36aab4"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\palette.dart", "hash": "cca4edc2c2c570f9d7e16c97a03c19a2"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\banner_theme.dart", "hash": "3130351c8333143b70539b0a0bef2c9d"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\plane.dart", "hash": "2a0078c9098cdc6357cbe70ce1642224"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\display_feature_sub_screen.dart", "hash": "fd9b487c6b333f8de2f5b2fbe769536f"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\consumer.dart", "hash": "38c2b67895c0418bce6750d3751a5b26"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\date_picker_theme.dart", "hash": "18dc56cd10e06f3640239ccdbe740364"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\focus_scope.dart", "hash": "d765dc0eb274578ea0585d7066a563d5"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\star_border.dart", "hash": "ed437fef28462d73a74c339fc59a6cda"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\borders.dart", "hash": "fa7146d472a712331eef3a404e2fabda"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\text_block_node.dart", "hash": "b61912427a6caf2305bdf145040013d7"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\binding.dart", "hash": "6a314e7c16349ce1a46736abaa73df76"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\app_bar_theme.dart", "hash": "89e311cb52134419a6ddf1fd93235ba5"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\will_pop_scope.dart", "hash": "4cf81f473e6af6b63729d72f41fe712e"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\layer.dart", "hash": "e037b8db819b66de43cfc6cfaaee04ca"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\route.dart", "hash": "a10e928bda19ab337bd21e3783f3bd2b"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\camera.dart", "hash": "a0037b1ded999402d8b2408ed0901bcd"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\shifted_box.dart", "hash": "********************************"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\context_menu_controller.dart", "hash": "4d7ff70b73cfe04b14f559266b5a991e"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_linux-2.4.1\\lib\\shared_preferences_linux.dart", "hash": "492280af61b4bca29e21d28db0c2be1c"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\ffi-2.1.4\\lib\\src\\utf8.dart", "hash": "329d62f7bbbfaf993dea464039ae886c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\bin\\cache\\artifacts\\material_fonts\\MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\devtools\\connectors\\component_snapshot_connector.dart", "hash": "5998ab184d6435835326c25134ae9502"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_android-4.7.0\\lib\\src\\weak_reference_utils.dart", "hash": "64d9fb212598826bd4334f4b374b8408"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\navigator_pop_handler.dart", "hash": "eb0361b270543f45fe1841c22539289f"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\has_collision_detection.dart", "hash": "52e0a1529620026e09065798af7b73d5"}, {"path": "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\.dart_tool\\flutter_build\\dart_plugin_registrant.dart", "hash": "b4c6e342fe1b30100b34aede98037bbb"}, {"path": "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\pubspec.yaml", "hash": "cb5b902e4dde1cdb0e220d1f581c0572"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\bitfield.dart", "hash": "abe3a12abba3d1b3a88ddb1ff43ea51e"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\animation\\animation_controller.dart", "hash": "c2253d864a54af232fe869aeafd95a61"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\effects.dart", "hash": "36ed5cf0c2ad591f2ef6845643317562"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\matrix2.dart", "hash": "ac51c125ed5881de5309794becbacc8b"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\path_set.dart", "hash": "1b20a6e406ca8e79675b2ebd9b362d10"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\experimental.dart", "hash": "750106b0d8434ec1cdd15b8f630a55d5"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\action_chip.dart", "hash": "51733e4ec61656353b10519c6af9ce23"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\icon_data.dart", "hash": "3a20b4d301e83ddf2a700e43c3d13f19"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\animation\\tween.dart", "hash": "deb256229ac9a60b1f5dee744af2411c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\text_selection.dart", "hash": "deb3149757b677ce04ef9df630a6f268"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\reassemble_handler.dart", "hash": "17dd5087a9b407563f662fc112624260"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\material.dart", "hash": "8bb77b9696ef695fe266346f1f4f818c"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\italic_text_node.dart", "hash": "a81e81ef6a7822e983857f14ebfc6a6b"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\boollist.dart", "hash": "206ef1a664f500f173416d5634d95c8b"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\geometry\\line_segment.dart", "hash": "44fddb953951e07f89400249740ba39b"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\tooltip.dart", "hash": "a1df4901b8e8927621e70484bed93522"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\autocomplete.dart", "hash": "8cf53323efec8458fddf0baf6ab7624e"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\double_tap_cancel_event.dart", "hash": "42b14898bc79ff4a3c910553b838aefd"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\style\\windows.dart", "hash": "0d86d4ba2e01e5e62f80fcf3e872f561"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\LICENSE", "hash": "3b954371d922e30c595d3f72f54bb6e4"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\math.dart", "hash": "a204dedd448ffe5ab5e8ead73798841d"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\tap_config.dart", "hash": "224ce1126570fcbe9efc14a6db677fe8"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_wkwebview-3.22.0\\lib\\src\\webkit_webview_controller.dart", "hash": "4095e94848be702cf591048e17421549"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\common\\utils.dart", "hash": "f2b212017a672c2cd895151f0dd6e0b3"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\sequence_effect.dart", "hash": "833f3d0f1ad4674302ae531498278f0a"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\geometry\\rectangle_component.dart", "hash": "f305332b8fc3a7237c7cd36a5d8ca818"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\bottom_tab_bar.dart", "hash": "d17cba679c8b1c94e10dfe2419b3d581"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\delayed_effect_controller.dart", "hash": "9d1ba27e27b92f40bed5932903e2f808"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_android-4.7.0\\lib\\src\\android_webview_cookie_manager.dart", "hash": "c034ed01855f4a0e5bb7b6b16075412d"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\multitap.dart", "hash": "8088a1e9032c8c67b16c9d46d58329fa"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\image_filter.dart", "hash": "61d6754850dbffcf4687388b630652ad"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\key.dart", "hash": "d3c9b4f0e500f74ef525ca325c595e66"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\service_extensions.dart", "hash": "ba0c8dd8a234a449050e76d22738480f"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\file_system_entity.dart", "hash": "04e7480fb89755fcc5f64f7d80ca610f"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\services.dart", "hash": "ea36b74bc426acaba86c810396c43f8f"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\has_world.dart", "hash": "1efb58d60110af3d256d0ab388847104"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\view.dart", "hash": "9601fa00a20190d932ac3e402cbd243c"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\proxy_provider.dart", "hash": "57b51f6f00c6bc3a29abbf83fbd804f8"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\audioplayers_windows-4.2.1\\LICENSE", "hash": "c40600261a3b45d01ebc98bcb0a6b2d5"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\post_process.dart", "hash": "f865960d6ef54a56ba9bc2b7a057ace5"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\tap_up_event.dart", "hash": "d66fe84d8198d52f88ff2e1ff00cbe6b"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\sprite_batch_component.dart", "hash": "2697474562d9085ecbbc0f3a19df5076"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\platform-3.1.6\\lib\\src\\testing\\fake_platform.dart", "hash": "f1a57183b9d9b863c00fcad39308d4c1"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\navigation_drawer_theme.dart", "hash": "d13b021628351bce6b686e67998d5eb3"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\text_selection_toolbar_anchors.dart", "hash": "bf2ed9d304c7f4d1e982b118bbe93bf2"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\clip_component.dart", "hash": "4a19d76136e006b5c4f2c5c990f6ac6f"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\sliver_padding.dart", "hash": "93ea53606b5bcb9f94323580b8a73a66"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\experimental\\layout_component.dart", "hash": "fdc268a163a1598112baa110741c9ade"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\http-1.4.0\\LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\autofill.dart", "hash": "900672c4f3a8c395331517ee4a59ea2c"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\src\\contrast_curve.dart", "hash": "9a12cf2a3549924510006db4651a1743"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\physics\\friction_simulation.dart", "hash": "9ee10f71d143dd2afab234cf329b6404"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\page_storage.dart", "hash": "7e1916e19ed191fb74d54f4e67662098"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\date.dart", "hash": "845617099022609b14f6ff93a5e4ddb0"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\unique_widget.dart", "hash": "0bc32d9519ad188655722c1b5df8e896"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\audioplayers_platform_interface-7.1.1\\LICENSE", "hash": "c40600261a3b45d01ebc98bcb0a6b2d5"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\router.dart", "hash": "e4d4fcce981dab2d3fb4a6a5532e2e78"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\platform_menu_bar.dart", "hash": "3199c921467a961819b9674fa5fcefe4"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector2.dart", "hash": "6a0fa6360b3aca8deb85dc7d88176eb8"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\overflow_bar.dart", "hash": "823b3b44cc184688e1fd926c923ada17"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\list_tile.dart", "hash": "c8fe24b1eb8cfbe5bcd7293c6991be37"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_fidelity.dart", "hash": "553c5e7dc9700c1fa053cd78c1dcd60a"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\composed_particle.dart", "hash": "b7046033b99555bb94b38fbca5d439ad"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\temperature\\temperature_cache.dart", "hash": "a6350a577e531a76d89b24942fca3073"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\team.dart", "hash": "193d1801b76c52075287857a01d3fe83"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\raw_keyboard_fuchsia.dart", "hash": "fb5f7eb90b6c4b891e8a1cda244c0a30"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\fps_text_component.dart", "hash": "593a38fb2161b36a88b2af64685f2e5b"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\style\\url.dart", "hash": "13c8dcc201f970674db72fbbd0505581"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\shaders\\ink_sparkle.frag", "hash": "7eee695ba96e5afa80abfaf59973617a"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\physics\\spring_simulation.dart", "hash": "976911a24895e34b12dc8b3227e70de9"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\list_body.dart", "hash": "3596cfc55c4bf6413b1781f6d498199d"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\semantics\\semantics_service.dart", "hash": "9ed5b69358848b7dc3a809fad7ffb031"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\drag_start_event.dart", "hash": "ade6821dd199eef830750e9f8357e164"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\debug.dart", "hash": "624431304ab3076b73b09e0b33e35e4f"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\post_process\\post_process.dart", "hash": "29063a772dde9d9a48d133c82bf50f7d"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\style\\posix.dart", "hash": "5e054086533f32f7181757a17890ae56"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\rectangle.dart", "hash": "6e8fc0a472d144571296194ec8c11960"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\lib\\src\\shared_preferences_async_android.dart", "hash": "5cfe2d9d61584eae2e9c8e81be1dd9c5"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\text_field.dart", "hash": "13a2211c81f2ef05128957664b806d54"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\move_by_effect.dart", "hash": "dfe3e6f9a514470a6d0aa05d3d4ae328"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\ffi-2.1.4\\LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\src\\point_provider_lab.dart", "hash": "6566a35ff0dea9376debf257bdb08fba"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\input\\joystick_component.dart", "hash": "3b4c1d3a0c06546a6e71bf7b9731b851"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\system_chrome.dart", "hash": "8f0501360ede174505eaf6fc29b19626"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\lints-5.1.1\\LICENSE", "hash": "4cb782b79f6fc5792728e331e81a3558"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\binding.dart", "hash": "e761fb30d10c79da8dbae27ffd274efc"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\ffi-2.1.4\\lib\\src\\allocation.dart", "hash": "9d62f4f58e8d63a8e106a1158eb13a02"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix3.dart", "hash": "447b270ddd29fa75f44c389fee5cadd1"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\notched_shapes.dart", "hash": "3afe080744ca0b8ff964e17b1ce3dec4"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\audioplayers_android-5.2.1\\LICENSE", "hash": "c40600261a3b45d01ebc98bcb0a6b2d5"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\tap_cancel_event.dart", "hash": "e9ec0f9cf17e05f72e9e616c1f5b8f7c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\semantics\\debug.dart", "hash": "0afbdd6f1125195ec28ff55922e51d50"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\motion.dart", "hash": "c2593e65f1a2367d83f0668470ab5f61"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\accelerated_particle.dart", "hash": "8d1d2b3824d27f7085cf22d726f20f9f"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\component_particle.dart", "hash": "c002f7727e35182699543218aecba580"}, {"path": "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders\\ink_sparkle.frag", "hash": "8af2b6b4632d42b4e1701ecda1a3448a"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\toggle_buttons.dart", "hash": "16ff0bd0c4c1f269ee8a2ed5a1707c37"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\add_event.g.dart", "hash": "413144882e92d0a27858427f45f214b9"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\title.dart", "hash": "ebbb5c061b2adb32764c3c2d1ec6ef3b"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\switch_theme.dart", "hash": "77c0e52dd42c70e9f234e8493da90d99"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\viewport.dart", "hash": "9d93cea34a3d8324122b5becaedf54fe"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\colors.dart", "hash": "5ed8acdae7dd3501b64b0ff3e33c1f45"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\magnifier.dart", "hash": "c7acb3b122d08afb8b40130e88a0dce7"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\vector_math_64.dart", "hash": "bd1315cfa157d271f8a38242c2abd0d9"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\size_changed_layout_notifier.dart", "hash": "3b88bbef523e78633182df3f70dbe5e4"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\button.dart", "hash": "492d0c49593bf708c5b65837efbabd39"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\nav_bar.dart", "hash": "1bcd67a0035393d4f31f28ac522ae83f"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\platform-3.1.6\\lib\\src\\interface\\platform.dart", "hash": "d2bab4c7d26ccfe4608fe8b47dd3b75c"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\style.dart", "hash": "bfb39b98783e4013d9fe5006de40874d"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\random_effect_controller.dart", "hash": "188910e9376e3dafca89b9607c5346f5"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\widget_state.dart", "hash": "b649f669a0c7234ba97f959808a59864"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\rotate_effect.dart", "hash": "e6f08831ef91f1d3feb0ea95f053e0fd"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_wkwebview-3.22.0\\lib\\src\\common\\weak_reference_utils.dart", "hash": "f6761cd4ee8d06fbf68ebccfb726a76c"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\viewfinder.dart", "hash": "3478c40404469c3bdee1628ebb731f68"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_android-4.7.0\\lib\\src\\android_ssl_auth_error.dart", "hash": "c3063db673fbe3c6c4c29108bb936a4f"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\aabb3.dart", "hash": "4d9f681599b9aba645421097eda46139"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\events.dart", "hash": "88647f5714cbc6735a1770c75a9a82d1"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\filled_button_theme.dart", "hash": "2b8123df092f0565cbb936af3168dc37"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\ink_decoration.dart", "hash": "cb9617d35408474cec5c44f6d57c0faa"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\segmented_button_theme.dart", "hash": "e0d86b7f5d94eb97a3a1f9393dd6b817"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\spell_check.dart", "hash": "7498ab451e1ca95d81055ac4e3a25079"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\lib\\shared_preferences_async_platform_interface.dart", "hash": "03664e80d73ff10d5787d9a828c87313"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\matrix3.dart", "hash": "64b9fc5ffdc9f1ba801b6ccf099347b1"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\core\\component_render_context.dart", "hash": "6da9c28771e31b099a5b02cabb1ba301"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences-2.5.3\\lib\\shared_preferences.dart", "hash": "698b47b813b0194cf3adacff5906a585"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\button.dart", "hash": "eabc0ff413b2fef64e2d0ce3554556be"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\cache\\value_cache.dart", "hash": "8ac9c29ddef242ae072263eac0ca382b"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\_html_element_view_io.dart", "hash": "aab7bf02fcfefca8bc2a8c24f574ceda"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences-2.5.3\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\parallax.dart", "hash": "b02d0e2d6cd47c6094a93c700e5b715f"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\divider_theme.dart", "hash": "34fbd21bc1ac0d1f0412b22e47041ece"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\sprite_animation_component.dart", "hash": "9b36f77aae7f3f58258f0e5b5f697489"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\sliver_group.dart", "hash": "bb8226c417f9729ca72c35b8c6b9e43b"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\experimental\\geometry\\shapes\\shape.dart", "hash": "f42e7807a53c09387e594571b7c26242"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\extensions.dart", "hash": "38e17b28106d00f831c56d4e78ca7421"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\feedback.dart", "hash": "65d7d9aae72d673e52ab256ebc374bf5"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\serialization.dart", "hash": "6557b0521d669043fe957bb915c97e38"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\input_date_picker_form_field.dart", "hash": "95454f3edcbc7a37488e2762d253a780"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\size_effect.dart", "hash": "d953f90382921ca586925d101e958eb8"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\keyboard_inserted_content.dart", "hash": "cb97906222d39edbd20f2db91ee3776e"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\menu_bar_theme.dart", "hash": "787f46e13d1a634284a8403fe1fbed15"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\scroll_text_box_component.dart", "hash": "********************************"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\elevated_button.dart", "hash": "064436cb7f327e308f036ef9b5c40b04"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\experimental\\geometry\\shapes\\rectangle.dart", "hash": "7e32c9b005ee75b5cb82e90e4ba093ed"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\resampler.dart", "hash": "d714b72a4f8f2b38a6144a6d8bfbfd74"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\error_codes.dart", "hash": "3e82e75a5b4bf22939d1937d2195a16e"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\selection_container.dart", "hash": "e2389f2925d99315a85f2e8494b95d95"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\win32_wrappers.dart", "hash": "af7270fd3861278053b1c45a7b66ece3"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\image_particle.dart", "hash": "35e3e41f2cb18730991d5cf20e5aa9e8"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\component_viewport_margin.dart", "hash": "c54861d022be854c1dd02c2af97ff4fe"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\devtools\\connectors\\game_loop_connector.dart", "hash": "3162926c90266e9ab85928222e119c4c"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\router\\overlay_route.dart", "hash": "45eab1347612bcb944cdc9dbf17ac83d"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\component_mixins\\double_tap_callbacks.dart", "hash": "9d64328554f210dcc26d76ba20c6b471"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\meta-1.16.0\\lib\\meta.dart", "hash": "aaace37762c25bcd679c2ab09129db12"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\text_input.dart", "hash": "********************************"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\sliver_persistent_header.dart", "hash": "cb6197499c79351555076b3a8ecbe352"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\slider.dart", "hash": "50f215628ea075eee0da174502e2853d"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\strikethrough_text_node.dart", "hash": "0d7519b5cfd89c5d1d99db92ec072edf"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\ordered_set-8.0.0\\lib\\ordered_set.dart", "hash": "8933513875721d55f5fbda0573c9e57c"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_vibrant.dart", "hash": "5b04f80518a8417cb87a0aec07dacf4f"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\widget_preview.dart", "hash": "f367fce949a6742ccbb25667c84c9b6d"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\ignore_events.dart", "hash": "23f7e38c120fe5d2285524d0c13d6311"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection.dart", "hash": "430a92b7fc96d89d9750f26f826484bc"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\devtools\\connectors\\component_count_connector.dart", "hash": "27775d37794fdd41fa7dcce84c1762c7"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\flexible_space_bar.dart", "hash": "24314571db406eb9ca7f0823eedbe105"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\collision_detection.dart", "hash": "a0c1d52f9d0c30331e24427920c383d5"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\raw_keyboard_linux.dart", "hash": "2ced57b0fa711aca80714d917cb75cb7"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\spell_check_suggestions_toolbar.dart", "hash": "bf3b061fba244f7be1a407f9a282d92e"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\material_dynamic_colors.dart", "hash": "81bf43e01741bf8b9df15ec37ffbc9ea"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\nested-1.0.0\\LICENSE", "hash": "753206f0b81e6116b384683823069537"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\post_process\\post_process_component.dart", "hash": "d29ba2f6d6a6ffafb083aee7444617d7"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\force_press.dart", "hash": "482df57db20848c7bbf606b03e258a69"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\text_element_component.dart", "hash": "cd4300d8cfa6155496c849820d403c9d"}, {"path": "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "hash": "52d67702b5ff2765444a313f26dbb851"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\routes.dart", "hash": "f96d4d30da8226fee102c4c76dddc6dc"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\platform_ssl_auth_error.dart", "hash": "32c0fe60f39c35b80cf6cac25832289b"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\notifier.dart", "hash": "19a6335712e4b2118b306dd8d0e8170d"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_expressive.dart", "hash": "be096140df774ec827218c6fe69b80e5"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\disposable_build_context.dart", "hash": "9f9fef5d5732d2b083ce7c05aff2e265"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_windows-2.4.1\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\wrap.dart", "hash": "e0e8407f511d45af90555246b991c170"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\path_provider_windows.dart", "hash": "38dc31b8820f5fd36eedbf7d9c1bf8d9"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\group_text_node.dart", "hash": "496688697978658dabcd05c3b2661b89"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\live_text.dart", "hash": "d970423c6488cba52d3585e0221e57ba"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\carousel.dart", "hash": "9af74e7990203514b88e8c03e2acede6"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\effect_target.dart", "hash": "fa0a0b7e33fe3e6778e3c6e9a53b08c3"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\tap_region.dart", "hash": "4205ba2309c32163616dd63f6264de11"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\image_icon.dart", "hash": "9f600f4245c898a4067344ec13a38169"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\fade_in_image.dart", "hash": "994fb9ad204eeea38bdc0040b37283f2"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\_timeline_io.dart", "hash": "2b2385e013688dc5ccafac580f8f6999"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\viewport.dart", "hash": "049721b49522de261cc343650a755864"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\desktop_text_selection_toolbar_button.dart", "hash": "4faf3d2a26408c96acccabef79b5080c"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\hct.dart", "hash": "596fb2e55b1ff1662e4bd67461fdc89d"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\font_loader.dart", "hash": "59471e40595be9401faf6958667e9baf"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\component_effect.dart", "hash": "926dc2b907e1fe087a6b5826a4b3b343"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\sliver_tree.dart", "hash": "8cc8a350c6fd356e165251a8754c95d5"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\checkbox_theme.dart", "hash": "********************************"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\form.dart", "hash": "764d664b6d2ebc59b9e54f316e64cc8c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\arc.dart", "hash": "06e19293b41e3c13506f5eacecfa4afc"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\parallax_component.dart", "hash": "acf61a9dc468ac08c545ce048a2cc8b1"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\raw_keyboard_windows.dart", "hash": "04e3db38362ad6238e0bd8394acf5b5e"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\input\\button_component.dart", "hash": "dcf5fa96ada3851d4105d6d7d4ee90ee"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\table.dart", "hash": "29139d4dafc54a4ce0f3bb186aec6841"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\context_menu_action.dart", "hash": "da8c02e232d7b61d4c38ed6f84826f17"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_iterator.dart", "hash": "6c54f90e0db5f42a13be6b3efeb4a04d"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\raw_keyboard_macos.dart", "hash": "4297b644d258ee7771676fea206a5118"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\change_notifier.dart", "hash": "d77e212c79f98b6ea9c29750e6e21643"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\renderers\\sprite_font_renderer.dart", "hash": "e42ec99b25abd637e12493af820849eb"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\device.dart", "hash": "fa4b87d898c61ffe4173022d2c2e5b49"}, {"path": "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\lib\\ui\\themes\\app_theme.dart", "hash": "33b818ba38c4932a196cea3807ad82f9"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\game_render_box.dart", "hash": "********************************"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\localizations.dart", "hash": "1652ed4c7d6405a0ff49b79d241a37f3"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\flame_game.dart", "hash": "0723cb022cb14b26c5f9a883cc1d703a"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\date_picker.dart", "hash": "ad4853392f910e8536f8a1bf485e2d14"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scroll_configuration.dart", "hash": "50666ddb8af05ad2fbc9f5980fad7785"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\clipboard.dart", "hash": "f63442b56372cdf284974de30fba136c"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\web_resource_error.dart", "hash": "4a496da43ada30e10f2394e8f52f4fa1"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\continuous_rectangle_border.dart", "hash": "c6361b93d4a266527cc473cc2570f714"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\text_selection_toolbar_layout_delegate.dart", "hash": "f9aa2eb956d270d4df8b3a7cd5ac52d7"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\fake_async-1.3.3\\LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\raw_keyboard_ios.dart", "hash": "f6879dbb0a0b22e90c61f21ebf5c440e"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\image_decoder.dart", "hash": "034c9a74f518b43df9ce8af9343c11cd"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\selection_area.dart", "hash": "fd1e888f48f9b2411ee9f3d8a5146397"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\javascript_message.dart", "hash": "f8107ebf50885cb4b6c1bb0bd35b0af7"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\sliver.dart", "hash": "bc7649ff9f28e8e22232071ca174f891"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\src\\hct_solver.dart", "hash": "b972c32590c642256132827def0b9923"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\radio_list_tile.dart", "hash": "c337b850a7c3d9b2239394993aeeab6d"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\component_mixins\\tap_callbacks.dart", "hash": "bb4746bc035297ab5cfaa58e0843a6fc"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\autofill.dart", "hash": "1772fb6df514c8211cbc291d7f4e529a"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\preferred_size.dart", "hash": "aff356351126de3409e033a766831f87"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\elements\\block_element.dart", "hash": "8d15e16689bcc5153f93f72f78a4bb30"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\cache\\memory_cache.dart", "hash": "b9b3a9b2d4bece24954ff8775f3cf0e0"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\calendar_date_picker.dart", "hash": "e07d9fca82a9a505388343f64824e06b"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\mouse_tracker.dart", "hash": "2abc41676d17f2f12e4878a82007cb3e"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\audioplayers_linux-4.2.1\\LICENSE", "hash": "c40600261a3b45d01ebc98bcb0a6b2d5"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\rendering\\paint_decorator.dart", "hash": "cccb3ab1f1c552bb040bc8beab68992a"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_arrow.g.dart", "hash": "2285a845b6ab95def71dcf8f121b806b"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\view.dart", "hash": "2d6edcb951c82d23cc3590b236a2e0f5"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\bottom_app_bar_theme.dart", "hash": "42e61179c9aef816ce39065a97feefac"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\switch.dart", "hash": "16421b0a1dc5136a0b6a71b8d63b3a27"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\physics\\clamped_simulation.dart", "hash": "d99d22e8a62a3ce1fa8b04b21702e1f6"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\tooltip_visibility.dart", "hash": "6ee7307afd79f7f32910b4db973200fe"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\button_style.dart", "hash": "399b64e5839e1a71d027672c0e0facc6"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\object.dart", "hash": "94d9e1be459979a63b16d671acb7b139"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\inherited_provider.dart", "hash": "dd618a65e1f3400d8224fedb42a1881b"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\focus_traversal.dart", "hash": "67b6dc5d338ed04947c83bdfa5e47db5"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\scrollbar.dart", "hash": "164146a96eb3ced8381be57fbad8ca63"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\geometry\\polygon_component.dart", "hash": "********************************"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\form_row.dart", "hash": "1ea047f0b3833f762bb5bff4a50d7707"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_wkwebview-3.22.0\\lib\\src\\common\\webkit_constants.dart", "hash": "61bc042503e65462e9ba5798a46ee5a1"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\viewports\\fixed_aspect_ratio_viewport.dart", "hash": "bf416a4f12626b128a2d5f3c3a2caff9"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\image_stream.dart", "hash": "fdf91b52ca7f1ba6753eb280113489f9"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\canonicalized_map.dart", "hash": "f5e7b04452b0066dff82aec6597afdc5"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\utilities.dart", "hash": "3f5e8feebce49c954d9c5ac1cda935c1"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\synchronized-3.3.1\\LICENSE", "hash": "8f29b74ba6fa81721ca1cd98cd39ae4d"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\platform_webview_controller_creation_params.dart", "hash": "b5a4bd755f0d3a44d4f4d0e8cddd26ab"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\grid_tile.dart", "hash": "36b808e976f035411a6b13a09cca7717"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\mouse_cursor.dart", "hash": "d268d6fb1caed2556859fc88f38f20d7"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\context.dart", "hash": "daeb052f1089d4e84d8a22acf56c1da2"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\tooltip_theme.dart", "hash": "e47677f74fdbf02610d897c45cbf26e8"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\game.dart", "hash": "7b77fa33c346ed62b485fca19c945389"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\drag_update_event.dart", "hash": "736cdb6b580040bc6cf48c1e4f516650"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\ink_splash.dart", "hash": "e91a73519c927b9535a83b357801e052"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\sprite_batch.dart", "hash": "1eb154b868500dab27b49789c2a43278"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\viewports\\fixed_resolution_viewport.dart", "hash": "64d9c04735c6a1ab91ced80147ff2ad9"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\_platform_io.dart", "hash": "34db9d7c9ebc27ae8cf7b0284f83365e"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\selectable_region.dart", "hash": "637c88e489948abd53ca429504bba60d"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\theme_data.dart", "hash": "dcb90d146b259cd68b023ffa77428ab8"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\LICENSE", "hash": "1bc3a9b4f64729d01f8d74a883befce2"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\src\\get_application_id_real.dart", "hash": "0e5b422d23b62b43ea48da9f0ad7fd47"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_web-2.4.3\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scroll_aware_image_provider.dart", "hash": "118be27ff90e0836323d9c71078cb122"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\google_mobile_ads-5.3.1\\LICENSE", "hash": "3b83ef96387f14655fc854ddc3c6bd57"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\rendering\\transform2d_decorator.dart", "hash": "daa0c4f2ffdaee7a185c41bbf90672a5"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\link.dart", "hash": "1f334b50f4df781bbbfab857581c3540"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\constants.dart", "hash": "df0a9878a16d3cd73cff00f496307544"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\range_slider.dart", "hash": "8de2935a1149d12828ad69c5e6ac5392"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\tab_view.dart", "hash": "d48d52bc573d346cad979541a2f68329"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\elements\\text_painter_text_element.dart", "hash": "3beed04642d35003a80bf1abde80b315"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_map.dart", "hash": "b6bcae6974bafba60ad95f20c12c72b9"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\segmented_button.dart", "hash": "c85987e2ad461c8b574f838c3e7a366d"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\icons.dart", "hash": "0fb5a81c723bbd506468f1ed5a811a48"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\physics\\simulation.dart", "hash": "0b594cddd30fe9d0d34a42f5328a2b38"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scroll_position.dart", "hash": "30e5fccf0da79e15eb5c9e94d97c6489"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\scaled_particle.dart", "hash": "9c7c9efd87b6461b3bfa16a5b47315ad"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\_web_image_info_io.dart", "hash": "c8ba4eb7463f2816b6b5c054b0876f2f"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\sliver_floating_header.dart", "hash": "29666cfc794fd78ec2462f291975ca43"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\typography.dart", "hash": "c0aa6aacbfe02a42de016558a0f011b4"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\spell_check_suggestions_toolbar_layout_delegate.dart", "hash": "d2232e4bd0457a4a3eb6bd897846eb44"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\ticker_provider.dart", "hash": "5772596554ad33f70cd612ea70ce40a1"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences-2.5.3\\lib\\src\\shared_preferences_legacy.dart", "hash": "4144d8b8e1cae585ab9f01406b3e1f75"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_wkwebview-3.22.0\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\animated_size.dart", "hash": "d8b218966d397dae64303cdd7d11b33b"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\scheduler\\service_extensions.dart", "hash": "5af5a2fb59f9c015abbda254a35ea7a6"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\anchor_to_effect.dart", "hash": "92da99d51352a88d5c5ec0914d517813"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\unmodifiable_wrappers.dart", "hash": "ea7c9cbd710872ba6d1b93050936bea7"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\empty_unmodifiable_set.dart", "hash": "0949b8197a6069783a78f4bb0a373fb0"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\bottom_navigation_bar_item.dart", "hash": "47474102c009e7099f3a9bf1d7ea8e06"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\stack_trace-1.12.1\\LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\clip.dart", "hash": "6790958a6027b5951c84e721543745a1"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\pages.dart", "hash": "540938d0d6c6e1ac0f1ae99b254c8423"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\progress_indicator_theme.dart", "hash": "0364ab7e57329ec7705fdf09d706de02"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\math\\random_fallback.dart", "hash": "4ce485b05a2efc9786b540e6a8429280"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\ffi-2.1.4\\lib\\src\\utf16.dart", "hash": "10969c23d56bc924ded3adedeb13ecff"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\characters.dart", "hash": "43268fa3ac45f3c527c72fc3822b9cb2"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\material_localizations.dart", "hash": "c32b3be564aac574be8ab4fde5dc7a2f"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\text_painter.dart", "hash": "75c871ac5db5adf93b61d050adad59a4"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\color_scheme.dart", "hash": "473a3ebe500198a12ebbc76b84ae9784"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\keyboard_handler.dart", "hash": "59aba83ed6df1f081853e320d8b65ce2"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\reorderable_list.dart", "hash": "9f2cdd0bd6f4ce722e2aaccb9516508c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\_bitfield_io.dart", "hash": "ae3ea28df9363a4eb82872e4a3573f77"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\styles\\background_style.dart", "hash": "a0db467f9bf9bafcc3a8a0d6296e1600"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\debug.dart", "hash": "7549ce8f21b73ad489ece8f0676cbddd"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\mixins\\has_performance_tracker.dart", "hash": "a0ef5967d4011b55b76eabec008e2a83"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\ordered_set-8.0.0\\lib\\comparing_ordered_set.dart", "hash": "5692892427cb0e919c3fb886119eec46"}, {"path": "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\.dart_tool\\flutter_build\\0c8992056cfdfeb44696f2a8efa5014a\\app.dill", "hash": "52d67702b5ff2765444a313f26dbb851"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scroll_notification_observer.dart", "hash": "23b4272b5eb55e3cf52556499d92ecad"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\error_codes_dart_io.dart", "hash": "9df03a340058a4e7792cd68745a4320c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scroll_delegate.dart", "hash": "05a65ef87faed70eb436b68ecd4a93f6"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\tweens.dart", "hash": "f9cf43c94d23a1e1093f4f1cfd789d18"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\math\\solve_cubic.dart", "hash": "80b9ead92770250c99df55751068160c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\collections.dart", "hash": "8764a2799f8899639fbf335bf794c627"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\eager.dart", "hash": "2a5ea48301e3de49528660d81bbad42c"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\platform_webview_widget_creation_params.dart", "hash": "002d9cfb5f6d6dfb64b0acbdb4763b88"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\src\\point_provider.dart", "hash": "7504c44d1fa6150901dd65ec78877be0"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\scheduler\\debug.dart", "hash": "da2fb5c7eaae1fd58b34e80c4dad2cca"}, {"path": "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "hash": "f2ee586785c834da116629800d92393a"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\standard_collision_detection.dart", "hash": "9ef0117591e0a3f02a6e8a452658b192"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\home_menu.g.dart", "hash": "07230264b6ad4306fed087103930fd35"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\restoration.dart", "hash": "7bc0cec528063323a299b9ee81543099"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\grid_tile_bar.dart", "hash": "90f2bfd8a277caf2dfdda31d1db65bf6"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\collision_callbacks.dart", "hash": "05ac150c68248d3ecfc6ec6485aad2ac"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\shader_warm_up.dart", "hash": "a5b2132b6c446c705361301cb9c2e261"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\cache.dart", "hash": "2d7c5297a62a0387dbfe31b287b41254"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\navigator.dart", "hash": "9d53bc53e1a3cc6a033ea11c76fba050"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_android-4.7.0\\lib\\webview_flutter_android.dart", "hash": "5daf4ebca71f4c004c7fcea6750a8425"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\aabb.dart", "hash": "4f8b273e300ce203ad20553dd8305b68"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\nested_scroll_view.dart", "hash": "aa39aa7acfb17e9127e921baa6a0f04e"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\drag_cancel_event.dart", "hash": "cbbe944035b190352c809bfb6f16b363"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\layout.dart", "hash": "ecb326e74cbc28af43432c1a98b4c820"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\matrix4.dart", "hash": "dc759a795f0c3731bf9234e4904045c9"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\anchor.dart", "hash": "aa7575ee22665d798b3ded7972c5405d"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\_capabilities_io.dart", "hash": "0b279dcbda1933bafe0bd7d322e2000f"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\material.dart", "hash": "62df7d5f15f6938d0e55d1d4548e9962"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\sprite_particle.dart", "hash": "e8a0d593b1759584d89e341cc937a57c"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\reverse_linear_effect_controller.dart", "hash": "fc1d25c15742e9d96e8ca80537742a3f"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scroll_activity.dart", "hash": "c5331010d9a48f689ab698e89196a9d7"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\load_request_params.dart", "hash": "c652b89f16c2597e1c3278b1e66e513a"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\system_context_menu.dart", "hash": "59a23ab4821a3fa4d98e0cc5c647667f"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\transform2d_effect.dart", "hash": "79a8255620fc5e1bee1fc88f39ca615c"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\aabb2.dart", "hash": "8a05c4ee4d75a485389f2e5c2f6618e6"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\overlay.dart", "hash": "7a018faf565163ffd45ba5e46e1d73ab"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\elevation_overlay.dart", "hash": "9b9ab2c0595d95cef9e27ae03e36991d"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector3.dart", "hash": "478e1071c9f577b6cabb8d72c36de077"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\desktop_text_selection.dart", "hash": "9849718d7826b98fa09e037accf9ae21"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\effect.dart", "hash": "04a22970a8f6a65cbef3ea4e327b93fa"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\physics\\tolerance.dart", "hash": "a481b9285f5d63f04e3b3e3fc2a6b44c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\restoration.dart", "hash": "e22a38dff2e5b45230581797ecf557e1"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\has_paint.dart", "hash": "155a2de627a518acb733765592a64517"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\matcher-0.12.17\\LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\asset_bundle.dart", "hash": "e1309fdfc73f3921dc1d2b212d57d217"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\page_scaffold.dart", "hash": "ab8431c89597be48ecb083aebdd9166a"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scrollable.dart", "hash": "254681ce32061015aea72e79d4a7b8ef"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_monochrome.dart", "hash": "66272a6751b167051ba879724cfe5749"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\switch_list_tile.dart", "hash": "b61a4dbd857b74aa723c96fc1b53fa90"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\isometric_tile_map_component.dart", "hash": "6a44166ede16bde464bd295e0e367ce0"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\colors.dart", "hash": "c3a5ae50b2ebde819d504e56afdfed77"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\grid_paper.dart", "hash": "a4c1cab2135ba3ea8d1385e894ed0d09"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\sliding_segmented_control.dart", "hash": "e3737de39b4843c820044597cd5f3b5f"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\picker.dart", "hash": "e0f8ae49e5195d1949887769d1e6117d"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\rect.dart", "hash": "90ab913e2d7a02ad1828b5be041c7bd2"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\single_child_scroll_view.dart", "hash": "bde473d632d84c502c0fc66dadf378bd"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\text_selection.dart", "hash": "575b2fdb620f6faf33b5a89b54898754"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\constants.dart", "hash": "aa4b5c0cdb6a66685350611b29ca9d38"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\rendering\\shadow3d_decorator.dart", "hash": "37e01f1b11b47cfb1c380363977439a4"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\matrix4.dart", "hash": "6250cc05770b9eca7a8010eaed7e5b94"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\function_effect.dart", "hash": "1c183419ddbe3589d364fec33fed37f9"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\decorated_sliver.dart", "hash": "b11666131177a6ebe97ffd60e3dac32a"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\checkbox_list_tile.dart", "hash": "********************************"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\hitboxes\\screen_hitbox.dart", "hash": "********************************"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\collision_passthrough.dart", "hash": "669fe73ae014fd35d5983eb7392cbb55"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\geometry\\circle_component.dart", "hash": "15db89ac321f43eb1b7d4d817f00935a"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\ray.dart", "hash": "5d9bdad87735a99fb4a503c5bee7c7fb"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\noise.dart", "hash": "e9fe7ebb2a16174d28ca146824370cec"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\flame_game_mixins\\multi_drag_dispatcher.dart", "hash": "a62d0cda86bad3394ede01cf74cec84f"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix4.dart", "hash": "b5f0b0da99e8a07d58c21ae071800404"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\timer.dart", "hash": "1335a05efb64c7e94ed102743bb0abdc"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\palettes\\tonal_palette.dart", "hash": "44b3c2a3d6e67a3213a49cce58fed932"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\flutter_logo.dart", "hash": "38d5b82b40702a77199cd5c8be506036"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\menu_anchor.dart", "hash": "7583d5890ed910b2c9a7ed24c1d1dce6"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\leak_tracker_flutter_testing-3.0.9\\LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\has_game_reference.dart", "hash": "7ade52c2611aba9d717ca1d4744beaf0"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\guid.dart", "hash": "55bb53dd4f9ed89c9ff88c204b59293c"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\stream_channel-2.1.4\\LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\game.dart", "hash": "29f81e417f66cf1b832def3a36f8ccc9"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\text_selection_toolbar.dart", "hash": "a0847dea6404a5a2413fb2690a3a614e"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\animated_switcher.dart", "hash": "bae66752808be6b5aaf6c0c266e9d40e"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\text_field.dart", "hash": "66d26b00d63b959556197f0f060e119e"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_fruit_salad.dart", "hash": "3c8d2d2b73f69d670141d376642e5252"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\geometry\\ray2.dart", "hash": "3f03c86aad1e2bac22f8d9e0585b60db"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\quad.dart", "hash": "739bb2e85022ddfb653590b93216942a"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\widget_span.dart", "hash": "13dd9b1f72f655ece9886a13c5bd2018"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\behaviors\\viewport_aware_bounds_behavior.dart", "hash": "91aa1cbde8492e3ca591dcf6c8b50a5f"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\lookup_boundary.dart", "hash": "e101d4f99c70b9f1ab98f5969e2c2111"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\header_node.dart", "hash": "f09b32570c5e7aae38ad388c3c4240c6"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\common\\sprite_font.dart", "hash": "a81a75885ce0eeef7a267bed19875226"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\linear_border.dart", "hash": "834ed7d1da7d9cd0a0af98bb92ee6231"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dislike\\dislike_analyzer.dart", "hash": "d7eb1678ec74acd9857a4193fd62ed5b"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\webview_cookie.dart", "hash": "667426285c5e989d91d20d4cb9ac4424"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\intersection_result.dart", "hash": "789e79772bba1132b3efdb60636a3ccb"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\error_helpers.dart", "hash": "73c0a59e2d19aea71c6029f871aa9f67"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\text_scaler.dart", "hash": "0fbda02660f6ca3d9bb5b9ce5ab88548"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\list_tile_theme.dart", "hash": "294a71aea06bc7ad5af2724726f746f7"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\game_mixins\\multi_touch_tap_detector.dart", "hash": "6f20571cc4dc29282c31c1a72ecb8ae2"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\lib\\src\\enums.dart", "hash": "f4b67c136a2189470329fd33ebe57cb3"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\box_fit.dart", "hash": "********************************"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\modal_barrier.dart", "hash": "dc8de4fb0846c2035cd04064058c8ef4"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\core\\component_key.dart", "hash": "687d23d66963be095a5ab8bcd159ee8a"}, {"path": "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\assets\\data\\facts.json", "hash": "5834c4cd4b6e3b9b215c87dde16d0413"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\cache\\assets_cache.dart", "hash": "94892425207e32623996c42a54842ece"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\constants.dart", "hash": "9a463f361999508124d9da4853b1ba5c"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\test_api-0.7.4\\LICENSE", "hash": "3323850953be5c35d320c2035aad1a87"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\navigation_bar_theme.dart", "hash": "ac7068865efbe78773199ef71b213440"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\src\\path_provider_linux.dart", "hash": "8ac537f4af05ad812e8cd29f077aee24"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\bin\\cache\\engine.stamp", "hash": "4436fe45d07530c8961e90a578571cfd"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\animated_size.dart", "hash": "e4743fa26fcac7a9adf779220fcd5e14"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\renderers\\text_renderer_factory.dart", "hash": "51051ed5d17d25bd9d809de15dee648d"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\platform_webview_permission_request.dart", "hash": "427bad4fc6f32394ca119b36556838b2"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\dropdown_menu_theme.dart", "hash": "166a96f7e7817372a8e04dc681869e61"}, {"path": "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\.dart_tool\\package_config_subset", "hash": "2d2395d6d5bc6014cd097b3d407a85a5"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\stack_frame.dart", "hash": "9f5e1e102c551e6d1bdd48d2f865db6f"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\change_notifier_provider.dart", "hash": "3ce0eeefa3058c1955fb1f435ce9928b"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\document_root.dart", "hash": "77adc40d06f8b14c394d82408dff6acb"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\oval_border.dart", "hash": "a4bd9e0add3bd13842fedfbd33ba0192"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\sliver.dart", "hash": "c3b78ed37835fb910da73d494152f0a3"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\debug\\time_track_component.dart", "hash": "317d91d37965406d383cde65f2f7ae8f"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\scribe.dart", "hash": "fc0d5385b1ef6f854be4e193437b39b6"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\triangle.dart", "hash": "e3f9a51488bca91a3350831c8ad6722f"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\http_response_error.dart", "hash": "d76f0b6a50f3fe2b8140468e37ae43b8"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\pointer_router.dart", "hash": "bacca4c03cd5121bb38b2cfcbde14197"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\back_button.dart", "hash": "18939fc6e74f9b391a45ae341fe30128"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\icons.dart", "hash": "9f7270100e64dae8750b9ae54cde56e4"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\paint.dart", "hash": "700e0d80e0a549d0dfa82c6f187e03ed"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\floating_action_button_location.dart", "hash": "f3a43f7f1e3cdd35326341d9fe51fdee"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\image_provider.dart", "hash": "7975814e8804652eda7f8b726d3913b2"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\obb3.dart", "hash": "5ca0b5786bf63efd4fc72fcecfe1b36c"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\elements\\rect_element.dart", "hash": "246ee194ea8c6e98d132dba307bf7ad9"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\badge_theme.dart", "hash": "8ba398f65bba2f40abf54b600e709975"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_android-4.7.0\\lib\\src\\android_webkit.g.dart", "hash": "584da82cd454dc921b8261920d12ed98"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\web-1.1.1\\LICENSE", "hash": "d53c45c14285d5ae1612c4146c90050b"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\mixins\\has_single_child_effect_controller.dart", "hash": "a2affc1db0442174e68d77d4b41ca394"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\list_extensions.dart", "hash": "9f8b50d98e75350b41d40fee06a9d7ed"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\observer_list.dart", "hash": "d70b537edfcda82387a991a99e6700d1"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\union_set.dart", "hash": "0073f703be7f7ddbd7f04d1b740f35c6"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\material_state.dart", "hash": "29dc810717daabf9c43e0c29a9535662"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\input_decorator.dart", "hash": "5fc29a61e77f85ac27eab0b592b2029e"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\iterable_extensions.dart", "hash": "5843b4750179f6099d443212b76f04a2"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\tap_down_event.dart", "hash": "cb6279f279abc799536311fcc8438853"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\priority_queue.dart", "hash": "34a4d340931147322eaddc77fdc65c22"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\interfaces\\multi_tap_listener.dart", "hash": "2f9eb698dee3d2ffe84f6ab51c241361"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\ink_well.dart", "hash": "9448b37dbe6df9b3e7d651a67b46578a"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\hitboxes\\circle_hitbox.dart", "hash": "********************************"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\drag_target.dart", "hash": "d932135a74692037b9bbbbb8fffffd5d"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\flame_drag_adapter.dart", "hash": "d55bf4141c207111ee0a494ff0ad99ae"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.1\\LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_file_system_entity.dart", "hash": "67918403456e9e1c17b3375ea708292c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\slider_value_indicator_shape.dart", "hash": "8288f862fad2f1a37af607a60b8ab395"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\default_text_editing_shortcuts.dart", "hash": "a334501a734a440f60aa69ce87071ef1"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\stadium_border.dart", "hash": "b2eb657328bd482e6b083d76dfbca76b"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\app_lifecycle_listener.dart", "hash": "59f3befbfab8a823bd8254bacd7cfca5"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\menu_button_theme.dart", "hash": "314c8a74e984655102d473063387e50e"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_android-4.7.0\\lib\\src\\android_webview_platform.dart", "hash": "96c6f74911d59baf6549ce5d84b04bc1"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\drag_boundary.dart", "hash": "f2045075d36258ce6ae2e6be59b46fed"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\default_selection_style.dart", "hash": "c64b32c068f0d8679ed2b8d699659780"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\floating_action_button_theme.dart", "hash": "c1329f33c1eee5551ec4b5b013167e71"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\file_system.dart", "hash": "3120b9b427a566f796573ee37167c026"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\viewports\\fixed_size_viewport.dart", "hash": "b1cd2f26e1d745c50481742dc8274b4d"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\javascript_console_message.dart", "hash": "8267479fdff528b1591cb141fcc999d5"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\utils\\string_utils.dart", "hash": "603b7b0647b2f77517d6e5cf1d073e5a"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\scrollbar_theme.dart", "hash": "04958562cca050e70dfde058bc4c8b3c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\expansible.dart", "hash": "21529000763daf1b8f70fd6bf123cf5a"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\computed_particle.dart", "hash": "f022c8c47e5a3c8c7c08c75501c229b6"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\about.dart", "hash": "69562fbf89b2450cf68b181723b06085"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\form_section.dart", "hash": "59ad3f592944fc890a2333c208b755a8"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_linux-2.2.1\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\debug.dart", "hash": "cbfb29e40dd6f4239a86817edb3e9033"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\performance_overlay.dart", "hash": "0071fe298793e366f6f5b16077adbf4c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\text_selection_toolbar_text_button.dart", "hash": "f06bc0318a4a0ecb95b7384aee5b21ca"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\lib\\src\\method_channel_path_provider.dart", "hash": "77ed8d7112753d0eeaa860ecd9fc5ba0"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\hardware_keyboard_detector.dart", "hash": "a02eb06baadf44ec322330ebc504b627"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\anchor_effect.dart", "hash": "821876c2abab9650411a3c51760ada1c"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.1\\lib\\path.dart", "hash": "********************************"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\magnifier.dart", "hash": "de9074b4c33d599d09ff4a2b953bc3c8"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\geometry.dart", "hash": "e704be62186964bb4ff16aea065f7d8e"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\dropdown.dart", "hash": "8d1fba4e53f7807f89979186ed5b8549"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\component_mixins\\hover_callbacks.dart", "hash": "2cffe4fe69e306464131584f9390a3b1"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\leak_tracker-10.0.9\\LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\flame.dart", "hash": "fce901fa174e715645f1f706ca685b4b"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\popup_menu_theme.dart", "hash": "1dd5b37860108a769459a04adbc418ed"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\source_span-1.10.1\\LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\path.dart", "hash": "6ecf0334fec7842fd736d3585d9f7133"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\constants.dart", "hash": "38a87ff489a47bc024400dc863be326d"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\world.dart", "hash": "881a88076a5892b810f17fb5aeafd7ef"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\system_sound.dart", "hash": "6c3746e3a4529b0367098600fb9d3d02"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_link.dart", "hash": "600a83d8e8dcbc1fde99887eea16f18e"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\devtools\\dev_tools_connector.dart", "hash": "9a7ace574aa92b248522a68f88ef4abf"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\javascript_log_level.dart", "hash": "d18499e5e3e572bcc05667dfece7dd8e"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\time_picker.dart", "hash": "3cd0fd2cdc8318929e613746e804999a"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\components_notifier.dart", "hash": "a108a5c74553d1341bc2ddfb215bcde9"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\deferred_component.dart", "hash": "f20b8958b0c35e9bbea75a43c9cf0e59"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\visibility.dart", "hash": "2535b76e646e95222aa41ab42951de84"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\component_mixins\\drag_callbacks.dart", "hash": "ce6bf5988a05a3926ccfbf4041b8915c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\drag.dart", "hash": "c6f3d5ab867a5b665a50e0af11d048d7"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\media_query.dart", "hash": "b071f66d17839dfc4b30db0836e7d5d6"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\lib\\types.dart", "hash": "13e6a7389032c839146b93656e2dd7a3"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\flavor.dart", "hash": "fc53938b545fafdfb7a5ef073a1254b5"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\annotations.dart", "hash": "b98145bd156a782be220cb3f652ba0a4"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\pop_scope.dart", "hash": "03e34b6476f2a5b43080c9fefcefe9ea"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\colors.dart", "hash": "b62b9e4874beca4adb96d6ebfd8e8dfb"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_link.dart", "hash": "733eb3422250897324028933a5d23753"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\box_decoration.dart", "hash": "********************************"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons\\animated_icons.dart", "hash": "933d793ffbf8f34eeb5f26962e83590f"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\haptic_feedback.dart", "hash": "de48d59f2b8a22c29dd37492fb4443ec"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\text_button.dart", "hash": "ae57ac152dc8bd45a57735cab6c0a634"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\flame_game_mixins\\multi_tap_dispatcher.dart", "hash": "13d099ab7ac3731845936a6c1b8babf8"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\has_decorator.dart", "hash": "69c4f4d7f4c3a32eb05e23a1ef77be6e"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\code_text_node.dart", "hash": "6a1e288acef9cf9167463922c58172b7"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\devtools\\connectors\\debug_mode_connector.dart", "hash": "c1729c028f171a98e19023133037b7fc"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\segmented_control.dart", "hash": "413dfcfb58932aef16c971e1148c1d3f"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_random_access_file.dart", "hash": "8584e5707c45dd6bdd567a10dfd8cd0d"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\debug.dart", "hash": "c15b0c06c9817813063ea4c1023d57fa"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\selector.dart", "hash": "6a72a2ba15880cab1e1d9a28a94f1a2d"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\rounded_rectangle_border.dart", "hash": "60129cbcd039cf3abac1ffaca12f3e55"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_wkwebview-3.22.0\\lib\\src\\webkit_webview_cookie_manager.dart", "hash": "8ff6f03325bdfdec53737df21703b33b"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\text_theme.dart", "hash": "b149267c83ef4fca8800213bc7331992"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\elements\\sprite_font_text_element.dart", "hash": "af275499eaafd7af16091a17e0eb9cb6"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\anchor_by_effect.dart", "hash": "e71a27efff8186adcadef6db0b0bb00f"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\placeholder_span.dart", "hash": "fbf98f47029acb307401fb5299babd1b"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\camera_component.dart", "hash": "a870967d6fa1a0736fd52101e1cab9ae"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\snapshot_widget.dart", "hash": "51b88420df990277e38f7347ce6392ca"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\particles.dart", "hash": "01bc98775d77e409a168c922dd9ee76b"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\platform_webview_cookie_manager_creation_params.dart", "hash": "0258e841a5e594b8720ba59a392bdff1"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\checkbox.dart", "hash": "********************************"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\stack.dart", "hash": "6169ff2b33fd5e84357f9296b4bca8a7"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\coordinate_transform.dart", "hash": "1bfa950b8fb118199f25b651919d024f"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\texture.dart", "hash": "e924e0f63c6bf206211fb6a6f319af53"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\blend\\blend.dart", "hash": "f487ad099842793e5deeebcc3a8048cb"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection_toolbar_button.dart", "hash": "9de52faf4ac5b8490e2763cc6969b95b"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\pinned_header_sliver.dart", "hash": "09bb22b1c2675b310a94dd1d4e8d6634"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\scrollbar.dart", "hash": "ef78266d8f88424f55e3135455ee90cf"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\icon_theme_data.dart", "hash": "2ed82d0ee4672e99dcdec5652737899f"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\sliver_list.dart", "hash": "46a5ea4411d0fef172edb219362737a3"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\image_cache.dart", "hash": "f0a22d14bdc393cf0ac6b80d64c2d742"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\text_selection_toolbar_button.dart", "hash": "8a5f786f5d88821fda1f83bc51501a31"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\intersection_result.dart", "hash": "832666b4f69945b957b6399ec677085b"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\sprite_animation.dart", "hash": "b8a79eca757c214c52ed069b5cf63fe4"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\debug.dart", "hash": "f2138801d3af6774b0cdb3ff8f966b8c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\_network_image_io.dart", "hash": "bb3c82c5a4ed9444e9e365800f49c19b"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\async.dart", "hash": "0a756bb43feaebfaf3245f96d8275789"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\text_span.dart", "hash": "68a71f0dca77a83028d4c2a3dff924da"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\gestures\\events.dart", "hash": "3d55fe3a2999625fc65cab1bf89404d5"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scrollbar.dart", "hash": "d7b4e641207a9ef63e51ef095e392a48"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\router\\world_route.dart", "hash": "94fb43a388bf6ccaabddac8094ebcad9"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\displacement_event.dart", "hash": "2320ab776f9b8d7a519e7948c2f35760"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\deferred_inherited_provider.dart", "hash": "59ae3a059b0ba1677002bed66f3b8c2d"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_tonal_spot.dart", "hash": "75f947f0ba87a0789a3ef91542bbc82c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\chip.dart", "hash": "bb30dae6bf385cbf0124346923f5c158"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\ray.dart", "hash": "146741f6f87d6612ee7bbf6a6fa9c119"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_file.dart", "hash": "3e30d0b7847f22c4b3674358052de8b5"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\canvas.dart", "hash": "82f5476d0b46ed3ee9df764fd75e7d0f"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\icon_theme_data.dart", "hash": "f8ded7a3f53ded600055288202a36535"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\outlined_button.dart", "hash": "917ff734e7ac8749015973f521ffcae4"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\context_menu.dart", "hash": "28d7ea566a4cd9f1e4455efd86f424b0"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\capabilities.dart", "hash": "ca40852851f196d4416da400314a610a"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\quaternion.dart", "hash": "698a6fc4361dd42bae9034c9c2b6cf7b"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\gestures.dart", "hash": "7b6199dff5808c0464b865fe03c4c616"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\search_view_theme.dart", "hash": "dccc4c7ff5d008afb92d48caaec777c8"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\velocity_tracker.dart", "hash": "d0a86046f0bc5262b7752d63d91a5005"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\elements\\inline_text_element.dart", "hash": "cd0021746b65a6cac56d73b5e23b9664"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\particle.dart", "hash": "73f4bc387e40bc959b6ac05995dc0713"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\single_child_particle.dart", "hash": "ebffd3cb0338f4d5ebcda954a5b9a61f"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\text_selection.dart", "hash": "89a52c840b1bed53ea3c5479d6190762"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\platform_navigation_delegate_creation_params.dart", "hash": "9ffc6ec5d7ca9434ae8fd53a12cde3b5"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\text_layout_metrics.dart", "hash": "dc64ebb08fcf29bdc05884be98af7daf"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\grapheme_clusters\\table.dart", "hash": "1f437276972808bf4cf722440da1b231"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flutter_lints-5.0.0\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\lib\\ui\\screens\\settings_screen.dart", "hash": "fd71c4edd92e0e5416688410f8361729"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\recognizer.dart", "hash": "2cd153115fb83cfe237aaec0909df2dc"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\sliver_prototype_extent_list.dart", "hash": "1cc862e37a6af483d4f60fdadd2f291d"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\lib\\path_provider_foundation.dart", "hash": "9485ecc20aafb0727c2700cf6e34cb65"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\constants.dart", "hash": "c6dd0c20e5521905acdd0e209727ec65"}, {"path": "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "hash": "85aa53b038be894edc8ed4b952643c56"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\src\\get_application_id.dart", "hash": "32f5f78e5648f98d8b602c6233aa4fc5"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\sliver_tree.dart", "hash": "391b9da05d70a8813ca2897c2600d0c3"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\circle_border.dart", "hash": "8ad25d6c38ddc8ce7abb828b97f4571a"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\activity_indicator.dart", "hash": "d65e92ce2b27908a96f7a6efbb9cdcb9"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\sine_effect_controller.dart", "hash": "1a8584b53d57b430c910a05625c8f42a"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\sliver_fill.dart", "hash": "40110052956a9ba346edc09b14dc1c14"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\webview_credential.dart", "hash": "35c15d26a69dec84849450a180b2ac7c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\flutter_version.dart", "hash": "85c3698c9fbb35da307a5ed29c9f0108"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scrollable_helpers.dart", "hash": "734c0e931a329ed28d4016070d47edcf"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\lib\\path_provider_platform_interface.dart", "hash": "09b3f3b1ef14ce885c016f2eba98f3da"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\circle_particle.dart", "hash": "57ca1699f9e94c01d6b1669dc756b8ca"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\game_widget\\gesture_detector_builder.dart", "hash": "c6f3fe1726730c92654755c7e327603e"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\box_shadow.dart", "hash": "********************************"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_android-2.2.17\\lib\\path_provider_android.dart", "hash": "eb368258f0f9fe56110bdc238488af97"}, {"path": "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\lib\\data\\services\\game_data_service.dart", "hash": "b9d23edbbf4cffef53274ecfb8894a4d"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_rainbow.dart", "hash": "0bc80db5885f9d8ecc0f80ddab6fe8b4"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\paragraph_node.dart", "hash": "0b1d4c9410f7d5ddccd083baf38a6e66"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\experimental\\geometry\\shapes\\rounded_rectangle.dart", "hash": "cd744ff88f1a83edf28f42b3f35b6e7f"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\implicit_animations.dart", "hash": "c613d7434cec89361a3b831cc91f86cc"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\paragraph.dart", "hash": "a7d808c966912115e46d4353372163dd"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\geometry\\constants.dart", "hash": "a6c8d064546e6e12c6d51d0b5c45c351"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector4.dart", "hash": "299bd3979d7999412945ac4e3199cdcf"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_android-2.2.17\\lib\\messages.g.dart", "hash": "3e127bbafbce223b6d416d5cca517df7"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\event_add.g.dart", "hash": "7c9757d3cc07fc4355bb72187af0413e"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\has_visibility.dart", "hash": "522b051e30186ffbf57657ac0c2e959f"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\broadphase\\quadtree\\has_quadtree_collision_detection.dart", "hash": "5da42dd1d15cd729bd67172bc7da6e28"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\animation\\tween_sequence.dart", "hash": "1245b0b05f60c57164018c5c918aa7d3"}, {"path": "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts\\MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\dual_transition_builder.dart", "hash": "99fb2e65ec78997546349e740526b24c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\safe_area.dart", "hash": "29eb69935c586ca665d0f6f7aa84a06d"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\refresh_indicator.dart", "hash": "9dfe03ebe247c2eb3420b00c80b61a44"}, {"path": "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\lib\\main.dart", "hash": "70a2a577af33e96d6ea979dda429069d"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\predictive_back_page_transitions_builder.dart", "hash": "99b984d75432877c137383cb96a09240"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\noise.dart", "hash": "206b1db3ce5f7b9e5efd220712f8d391"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\broadphase\\quadtree\\quadtree_collision_detection.dart", "hash": "d06deb4ceabbc2a93bc3b5b9d58aa47e"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\navigation_toolbar.dart", "hash": "6e144abc5846b7e5eda10cec79c9051a"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\LICENSE", "hash": "753206f0b81e6116b384683823069537"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\image_composition.dart", "hash": "289960f3f44f284a2b49b91fa0aeb4f5"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\object.dart", "hash": "b614d8172098403c683c68aafa3e92e8"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\path_provider_linux.dart", "hash": "b48ba72a2d5d084d297c3d78e351036e"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\path_map.dart", "hash": "9d273d5a3c1851b0313cd949e7f84355"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\variant.dart", "hash": "8dea906a9b8773920b6d1ccea59807bf"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\styles\\block_style.dart", "hash": "12eb7b90274154bb5318b287a3144370"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\adaptive_text_selection_toolbar.dart", "hash": "f0bf47fdaf6f5a5be43f49d2d73a3145"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\service_extensions.dart", "hash": "217b7c2fd7b1eccde5897e1f17fdccf9"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\lib\\src\\strings.dart", "hash": "4e96c754178f24bd4f6b2c16e77b3a21"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\decorated_sliver.dart", "hash": "833549c8f32a60f8bc7d2f221760844f"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\action_buttons.dart", "hash": "e71bfc49f52579c2e458315e9067527e"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\material_state_mixin.dart", "hash": "769e3974284ea91f60d3322d7322a979"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\devtool.dart", "hash": "2d7d80b5c908559a133f8729b6e755c0"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\sprite_animation_group_component.dart", "hash": "e13497faa91cac97411fe3f5cc9fb452"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\message_codecs.dart", "hash": "7a804325c8972a8d7a59f08a66fb672c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\animation\\curves.dart", "hash": "6ac08531373f9a0cf8243363385bdab2"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\styles\\document_style.dart", "hash": "451df38d8a108532fb52d31fff9d15ec"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\union_set_controller.dart", "hash": "f301af2d0392296f456363085becbf47"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\dialog_theme.dart", "hash": "8141617d0d061a10cb35d7c324354241"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\_background_isolate_binary_messenger_io.dart", "hash": "7a8436b3682b625fdf4b1dbccda9e895"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\event.dart", "hash": "b4858c17170fae03a8942f9e10ca277e"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\chip_theme.dart", "hash": "ee424e7e1baf8744e2386a37728238fc"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\undo_manager.dart", "hash": "cdab58168d88bf8cc4f48120b49ae69c"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\scheduler\\binding.dart", "hash": "fe8a2d81943a5849c5e939a993b7dd39"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\icon_tree_shaker.dart", "hash": "ba72bd9a2dac60d690accde65a1ba704"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\custom_text_node.dart", "hash": "be23d40892d8c78332d2c729b05b78c7"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_home.g.dart", "hash": "59ba7bdfc57111a2c59ae46a61e0daa1"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\popup_menu.dart", "hash": "b430b3c9acbccd3305bde7d5057b8c3c"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\translated_particle.dart", "hash": "11626edb5c1d7d45f9e4203d51f6ce39"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\scale.dart", "hash": "57ef3d363dc1ad0dd0d7c9f3042b66cc"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\inherited_theme.dart", "hash": "e657634a020bb8214e2507b5d0105d6b"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\characters-1.4.0\\lib\\characters.dart", "hash": "fa2a57b3b873fb7db4b8b961735e4ca3"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\desktop_text_selection_toolbar_layout_delegate.dart", "hash": "6c685d31c42c2f60c5082e01574011e4"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\aabb3.dart", "hash": "257ca4608e7d75f1db8d4c3ab710ac70"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\ordered_set-8.0.0\\lib\\read_only_ordered_set.dart", "hash": "ba875a7ee97a2f83528dd30d90b5fc43"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\text_node.dart", "hash": "da89c269585ce84f59481264fb79d58e"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\router\\router_component.dart", "hash": "60737ca9ffc5a64a46cc4e57d977a516"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\list_wheel_viewport.dart", "hash": "697b345e3b00008c024c87f80dc0b87f"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\input\\hud_button_component.dart", "hash": "afe1b1500b4847bcae0091352a52c22b"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\devtools\\connectors\\component_tree_connector.dart", "hash": "09881a8fcd78fcdd4721070b226e5221"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\frustum.dart", "hash": "218ecb2798a6fb1ec08cd5c993d98269"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_wkwebview-3.22.0\\lib\\src\\webkit_ssl_auth_error.dart", "hash": "7c95e48cc027f2d2dd9053c57fcad107"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\http_parser-4.1.2\\LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\async-2.13.0\\LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\core\\recycled_queue.dart", "hash": "a50927e03626adebf10b3aaf5867821a"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\common.dart", "hash": "493b51476fc266d10a636f520fff01fc"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_linux-2.4.1\\LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\snapshot.dart", "hash": "9ffbd8549fd336efe34d0506d2da6dc0"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\drawer_header.dart", "hash": "4082637928b7b78efd6c2bf895818e6e"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\text_button_theme.dart", "hash": "77c012e51ce5fb5b6758ed47ee29af56"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\expansion_tile_theme.dart", "hash": "828a01a48b4a0451b0d1f9c531cc292c"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme.dart", "hash": "a6adbe3868e017441360895c35fd6aa2"}, {"path": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\tabs.dart", "hash": "3395f23737e3d211de3f452d3724d939"}, {"path": "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\flame_game_mixins\\pointer_move_dispatcher.dart", "hash": "c51f184e143d7843e899b1954300c548"}]}