{"inputs": ["F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\.dart_tool\\package_config_subset", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\common.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\bin\\cache\\engine.stamp", "f:\\Projetos\\fvm\\versions\\3.32.4\\bin\\cache\\engine.stamp", "f:\\Projetos\\fvm\\versions\\3.32.4\\bin\\cache\\engine.stamp", "f:\\Projetos\\fvm\\versions\\3.32.4\\bin\\cache\\engine.stamp", "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\lib\\main.dart", "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\.dart_tool\\flutter_build\\dart_plugin_registrant.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\dart_plugin_registrant.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\material.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\provider.dart", "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\lib\\game\\daily_fact_fling_game.dart", "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\lib\\ui\\screens\\main_menu_screen.dart", "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\lib\\ui\\themes\\app_theme.dart", "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\lib\\data\\services\\game_data_service.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_android-2.2.17\\lib\\path_provider_android.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\lib\\shared_preferences_android.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_android-4.7.0\\lib\\webview_flutter_android.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\lib\\path_provider_foundation.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\lib\\shared_preferences_foundation.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_wkwebview-3.22.0\\lib\\webview_flutter_wkwebview.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\path_provider_linux.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_linux-2.4.1\\lib\\shared_preferences_linux.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\path_provider_windows.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_windows-2.4.1\\lib\\shared_preferences_windows.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\about.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\action_buttons.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\action_chip.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\action_icons_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\adaptive_text_selection_toolbar.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\app.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\app_bar.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\app_bar_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\arc.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\autocomplete.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\badge.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\badge_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\banner.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\banner_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\bottom_app_bar.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\bottom_app_bar_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\bottom_navigation_bar.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\bottom_navigation_bar_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\bottom_sheet.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\bottom_sheet_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\button.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\button_bar.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\button_bar_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\button_style.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\button_style_button.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\button_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\calendar_date_picker.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\card.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\card_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\carousel.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\checkbox.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\checkbox_list_tile.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\checkbox_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\chip.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\chip_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\choice_chip.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\circle_avatar.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\color_scheme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\colors.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\constants.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\curves.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\data_table.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\data_table_source.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\data_table_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\date.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\date_picker.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\date_picker_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\debug.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\desktop_text_selection.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\desktop_text_selection_toolbar.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\desktop_text_selection_toolbar_button.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\dialog.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\dialog_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\divider.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\divider_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\drawer.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\drawer_header.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\drawer_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\dropdown.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\dropdown_menu.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\dropdown_menu_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\elevated_button.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\elevated_button_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\elevation_overlay.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\expand_icon.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\expansion_panel.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\expansion_tile.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\expansion_tile_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\filled_button.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\filled_button_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\filter_chip.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\flexible_space_bar.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\floating_action_button.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\floating_action_button_location.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\floating_action_button_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\grid_tile.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\grid_tile_bar.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\icon_button.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\icon_button_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\icons.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\ink_decoration.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\ink_highlight.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\ink_ripple.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\ink_sparkle.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\ink_splash.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\ink_well.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\input_border.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\input_chip.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\input_date_picker_form_field.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\input_decorator.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\list_tile.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\list_tile_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\magnifier.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\material.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\material_button.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\material_localizations.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\material_state.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\material_state_mixin.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\menu_anchor.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\menu_bar_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\menu_button_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\menu_style.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\menu_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\mergeable_material.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\motion.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\navigation_bar.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\navigation_bar_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\navigation_drawer.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\navigation_drawer_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\navigation_rail.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\navigation_rail_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\no_splash.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\outlined_button.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\outlined_button_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\page.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\page_transitions_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\paginated_data_table.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\popup_menu.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\popup_menu_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\predictive_back_page_transitions_builder.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\progress_indicator.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\progress_indicator_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\radio.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\radio_list_tile.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\radio_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\range_slider.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\refresh_indicator.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\reorderable_list.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\scaffold.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\scrollbar.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\scrollbar_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\search.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\search_anchor.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\search_bar_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\search_view_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\segmented_button.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\segmented_button_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\selectable_text.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\selection_area.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\shadows.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\slider.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\slider_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\slider_value_indicator_shape.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\snack_bar.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\snack_bar_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\spell_check_suggestions_toolbar.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\spell_check_suggestions_toolbar_layout_delegate.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\stepper.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\switch.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\switch_list_tile.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\switch_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\tab_bar_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\tab_controller.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\tab_indicator.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\tabs.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\text_button.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\text_button_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\text_field.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\text_form_field.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\text_selection.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\text_selection_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\text_selection_toolbar.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\text_selection_toolbar_text_button.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\text_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\theme_data.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\time.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\time_picker.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\time_picker_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\toggle_buttons.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\toggle_buttons_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\tooltip.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\tooltip_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\tooltip_visibility.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\typography.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\user_accounts_drawer_header.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\widgets.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\async_provider.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\change_notifier_provider.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\consumer.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\listenable_provider.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\provider.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\proxy_provider.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\reassemble_handler.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\selector.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\value_listenable_provider.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\game.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\events.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\components.dart", "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\lib\\utils\\constants.dart", "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\lib\\data\\models\\game_fact.dart", "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\lib\\ui\\screens\\game_screen.dart", "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\lib\\ui\\screens\\how_to_play_screen.dart", "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\lib\\ui\\screens\\settings_screen.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\services.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences-2.5.3\\lib\\shared_preferences.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\lib\\path_provider_platform_interface.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_android-2.2.17\\lib\\messages.g.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\lib\\src\\shared_preferences_android.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\lib\\src\\shared_preferences_async_android.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_android-4.7.0\\lib\\src\\android_ssl_auth_error.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_android-4.7.0\\lib\\src\\android_webview_controller.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_android-4.7.0\\lib\\src\\android_webview_cookie_manager.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_android-4.7.0\\lib\\src\\android_webview_platform.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\foundation.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\lib\\messages.g.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\lib\\src\\shared_preferences_async_foundation.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\lib\\src\\shared_preferences_foundation.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_wkwebview-3.22.0\\lib\\src\\webkit_ssl_auth_error.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_wkwebview-3.22.0\\lib\\src\\webkit_webview_controller.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_wkwebview-3.22.0\\lib\\src\\webkit_webview_cookie_manager.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_wkwebview-3.22.0\\lib\\src\\webkit_webview_platform.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\src\\path_provider_linux.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\file.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\local.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.1\\lib\\path.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\lib\\shared_preferences_async_platform_interface.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\lib\\shared_preferences_platform_interface.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\lib\\types.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\folders.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\path_provider_windows_real.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\cupertino.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\scheduler.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\back_button.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\rendering.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons\\animated_icons.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons\\animated_icons_data.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\add_event.g.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\arrow_menu.g.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\close_menu.g.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\ellipsis_search.g.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\event_add.g.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\home_menu.g.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\list_view.g.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_arrow.g.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_close.g.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_home.g.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\pause_play.g.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\play_pause.g.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\search_ellipsis.g.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\view_list.g.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\animation.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\vector_math_64.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\gestures.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\collection.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\material_color_utilities.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\painting.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\characters-1.4.0\\lib\\characters.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\actions.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\adapter.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\animated_cross_fade.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\animated_scroll_view.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\animated_size.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\animated_switcher.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\annotated_region.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\app.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\app_lifecycle_listener.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\async.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\autocomplete.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\autofill.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\automatic_keep_alive.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\banner.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\basic.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\binding.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\bottom_navigation_bar_item.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\color_filter.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\container.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\context_menu_button_item.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\context_menu_controller.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\debug.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\decorated_sliver.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\default_selection_style.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\default_text_editing_shortcuts.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\desktop_text_selection_toolbar_layout_delegate.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\dismissible.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\display_feature_sub_screen.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\disposable_build_context.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\drag_boundary.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\drag_target.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\draggable_scrollable_sheet.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\dual_transition_builder.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\editable_text.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\expansible.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\fade_in_image.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\feedback.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\flutter_logo.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\focus_manager.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\focus_scope.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\focus_traversal.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\form.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\framework.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\gesture_detector.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\grid_paper.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\heroes.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\icon.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\icon_data.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\icon_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\icon_theme_data.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\image.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\image_filter.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\image_icon.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\implicit_animations.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\inherited_model.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\inherited_notifier.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\inherited_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\interactive_viewer.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\keyboard_listener.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\layout_builder.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\list_wheel_scroll_view.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\localizations.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\lookup_boundary.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\magnifier.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\media_query.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\modal_barrier.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\navigation_toolbar.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\navigator.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\navigator_pop_handler.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\nested_scroll_view.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\notification_listener.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\orientation_builder.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\overflow_bar.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\overlay.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\overscroll_indicator.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\page_storage.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\page_view.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\pages.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\performance_overlay.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\pinned_header_sliver.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\placeholder.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\platform_menu_bar.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\platform_selectable_region_context_menu.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\platform_view.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\pop_scope.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\preferred_size.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\primary_scroll_controller.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\raw_keyboard_listener.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\raw_menu_anchor.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\reorderable_list.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\restoration.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\restoration_properties.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\router.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\routes.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\safe_area.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scroll_activity.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scroll_aware_image_provider.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scroll_configuration.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scroll_context.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scroll_controller.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scroll_delegate.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scroll_metrics.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scroll_notification.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scroll_notification_observer.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scroll_physics.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scroll_position.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scroll_position_with_single_context.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scroll_simulation.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scroll_view.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scrollable.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scrollable_helpers.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\scrollbar.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\selectable_region.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\selection_container.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\semantics_debugger.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\service_extensions.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\shared_app_data.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\shortcuts.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\single_child_scroll_view.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\size_changed_layout_notifier.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\sliver.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\sliver_fill.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\sliver_floating_header.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\sliver_layout_builder.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\sliver_persistent_header.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\sliver_prototype_extent_list.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\sliver_resizing_header.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\sliver_tree.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\slotted_render_object_widget.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\snapshot_widget.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\spacer.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\spell_check.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\standard_component_type.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\status_transitions.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\system_context_menu.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\table.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\tap_region.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\text.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\text_editing_intents.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\text_selection.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\text_selection_toolbar_anchors.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\text_selection_toolbar_layout_delegate.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\texture.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\ticker_provider.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\title.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\toggleable.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\transitions.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\tween_animation_builder.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\two_dimensional_scroll_view.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\two_dimensional_viewport.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\undo_history.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\unique_widget.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\value_listenable_builder.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\view.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\viewport.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\visibility.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\widget_inspector.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\widget_preview.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\widget_span.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\widget_state.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\will_pop_scope.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\nested-1.0.0\\lib\\nested.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\deferred_inherited_provider.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\devtool.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\provider-6.1.5\\lib\\src\\inherited_provider.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\has_collision_detection.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\router\\overlay_route.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\router\\route.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\router\\router_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\router\\value_route.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\router\\world_route.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\vector2.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\flame_game.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\game.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\game_widget\\game_widget.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\mixins\\has_performance_tracker.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\mixins\\single_game_instance.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\notifying_vector2.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\transform2d.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\renderers\\text_paint.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\component_mixins\\double_tap_callbacks.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\component_mixins\\drag_callbacks.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\component_mixins\\hover_callbacks.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\component_mixins\\pointer_move_callbacks.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\component_mixins\\tap_callbacks.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\flame_game_mixins\\double_tap_dispatcher.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\flame_game_mixins\\multi_drag_dispatcher.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\flame_game_mixins\\multi_tap_dispatcher.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\flame_game_mixins\\pointer_move_dispatcher.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\game_mixins\\multi_touch_drag_detector.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\game_mixins\\multi_touch_tap_detector.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\hardware_keyboard_detector.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\interfaces\\multi_drag_listener.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\interfaces\\multi_tap_listener.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\double_tap_cancel_event.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\double_tap_down_event.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\double_tap_event.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\drag_cancel_event.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\drag_end_event.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\drag_start_event.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\drag_update_event.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\pointer_move_event.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\tap_cancel_event.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\tap_down_event.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\tap_up_event.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\mixins\\keyboard.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\gestures\\detectors.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\gestures\\events.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\anchor.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\camera_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\world.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\hitboxes\\screen_hitbox.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\clip_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\components_notifier.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\core\\component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\core\\component_key.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\custom_painter_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\fps_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\fps_text_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\input\\advanced_button_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\input\\joystick_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\input\\keyboard_listener_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\input\\toggle_button_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\isometric_tile_map_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\component_viewport_margin.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\coordinate_transform.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\gesture_hitboxes.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\has_ancestor.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\has_decorator.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\has_game_ref.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\has_game_reference.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\has_paint.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\has_time_scale.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\has_visibility.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\has_world.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\ignore_events.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\keyboard_handler.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\notifier.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\parent_is_a.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\single_child_particle.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\mixins\\snapshot.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\nine_tile_box_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\parallax_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\particle_system_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\position_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\scroll_text_box_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\spawn_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\sprite_animation_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\sprite_animation_group_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\sprite_batch_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\sprite_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\sprite_group_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\text_box_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\text_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\text_element_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\timer_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\geometry\\circle_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\geometry\\polygon_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\geometry\\rectangle_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\geometry\\shape_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\timer.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\asset_bundle.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\asset_manifest.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\autofill.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\binary_messenger.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\binding.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\browser_context_menu.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\clipboard.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\debug.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\deferred_component.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\flavor.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\flutter_version.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\font_loader.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\haptic_feedback.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\hardware_keyboard.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\keyboard_inserted_content.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\keyboard_key.g.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\keyboard_maps.g.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\live_text.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\message_codec.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\message_codecs.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\mouse_cursor.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\mouse_tracking.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\platform_channel.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\platform_views.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\predictive_back_event.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\process_text.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\raw_keyboard.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\raw_keyboard_android.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\raw_keyboard_fuchsia.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\raw_keyboard_ios.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\raw_keyboard_linux.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\raw_keyboard_macos.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\raw_keyboard_web.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\raw_keyboard_windows.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\restoration.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\scribe.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\service_extensions.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\spell_check.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\system_channels.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\system_chrome.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\system_navigator.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\system_sound.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\text_boundary.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\text_editing.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\text_editing_delta.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\text_formatter.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\text_input.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\text_layout_metrics.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\undo_manager.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences-2.5.3\\lib\\src\\shared_preferences_async.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences-2.5.3\\lib\\src\\shared_preferences_legacy.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\plugin_platform_interface-2.1.8\\lib\\plugin_platform_interface.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\lib\\src\\enums.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\lib\\src\\method_channel_path_provider.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\lib\\src\\messages.g.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\lib\\src\\strings.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\lib\\src\\messages_async.g.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\meta-1.16.0\\lib\\meta.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\webview_flutter_platform_interface.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_android-4.7.0\\lib\\src\\android_webkit.g.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_android-4.7.0\\lib\\src\\android_proxy.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_android-4.7.0\\lib\\src\\android_webkit_constants.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_android-4.7.0\\lib\\src\\platform_views_service_proxy.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_android-4.7.0\\lib\\src\\weak_reference_utils.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\annotations.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\assertions.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\basic_types.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\binding.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\bitfield.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\capabilities.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\change_notifier.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\collections.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\consolidate_response.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\constants.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\debug.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\diagnostics.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\isolates.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\key.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\licenses.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\memory_allocations.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\node.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\object.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\observer_list.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\persistent_hash_map.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\platform.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\print.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\serialization.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\service_extensions.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\stack_frame.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\synchronous_future.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\timeline.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\unicode.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\lib\\src\\messages.g.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_wkwebview-3.22.0\\lib\\src\\common\\web_kit.g.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_wkwebview-3.22.0\\lib\\src\\webkit_proxy.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_wkwebview-3.22.0\\lib\\src\\common\\platform_webview.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_wkwebview-3.22.0\\lib\\src\\common\\weak_reference_utils.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_wkwebview-3.22.0\\lib\\src\\common\\webkit_constants.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\xdg_directories-1.1.0\\lib\\xdg_directories.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\src\\get_application_id.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\context.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\style.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\path_exception.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\path_map.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\path_set.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\lib\\method_channel_shared_preferences.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\ffi-2.1.4\\lib\\ffi.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\guid.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\win32_wrappers.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\activity_indicator.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\adaptive_text_selection_toolbar.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\app.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\bottom_tab_bar.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\button.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\checkbox.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\colors.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\constants.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\context_menu.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\context_menu_action.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\date_picker.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\debug.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection_toolbar.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection_toolbar_button.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\dialog.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\form_row.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\form_section.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\icon_theme_data.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\icons.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\interface_level.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\list_section.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\list_tile.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\localizations.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\magnifier.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\nav_bar.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\page_scaffold.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\picker.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\radio.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\refresh.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\route.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\scrollbar.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\search_field.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\segmented_control.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\sheet.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\slider.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\sliding_segmented_control.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\spell_check_suggestions_toolbar.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\switch.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\tab_scaffold.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\tab_view.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\text_field.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\text_form_field_row.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\text_selection.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\text_selection_toolbar.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\text_selection_toolbar_button.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\text_theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\theme.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\cupertino\\thumb_painter.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\scheduler\\binding.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\scheduler\\debug.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\scheduler\\priority.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\scheduler\\service_extensions.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\scheduler\\ticker.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\semantics.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\animated_size.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\binding.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\box.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\custom_layout.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\custom_paint.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\debug.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\debug_overflow_indicator.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\decorated_sliver.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\editable.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\error.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\flex.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\flow.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\image.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\layer.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\layout_helper.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\list_body.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\list_wheel_viewport.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\mouse_tracker.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\object.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\paragraph.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\performance_overlay.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\platform_view.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\proxy_box.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\proxy_sliver.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\rotated_box.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\selection.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\service_extensions.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\shifted_box.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\sliver.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\sliver_fill.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\sliver_fixed_extent_list.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\sliver_grid.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\sliver_group.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\sliver_list.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\sliver_multi_box_adaptor.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\sliver_padding.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\sliver_persistent_header.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\sliver_tree.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\stack.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\table.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\table_border.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\texture.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\tweens.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\view.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\viewport.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\viewport_offset.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\rendering\\wrap.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\animation\\animation.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\animation\\animation_controller.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\animation\\animation_style.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\animation\\animations.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\animation\\curves.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\animation\\listener_helpers.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\animation\\tween.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\animation\\tween_sequence.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\aabb2.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\aabb3.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\colors.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\constants.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\error_helpers.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\frustum.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\intersection_result.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix2.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix3.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix4.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\noise.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\obb3.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\opengl.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\plane.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\quad.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\quaternion.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\ray.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\sphere.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\triangle.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\utilities.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector2.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector3.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector4.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\arena.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\binding.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\constants.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\converter.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\debug.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\drag.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\drag_details.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\eager.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\events.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\force_press.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\gesture_settings.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\hit_test.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\long_press.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\lsq_solver.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\monodrag.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\multidrag.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\multitap.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\pointer_router.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\pointer_signal_resolver.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\recognizer.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\resampler.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\scale.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\tap.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\tap_and_drag.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\team.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\gestures\\velocity_tracker.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\algorithms.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\boollist.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\canonicalized_map.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_iterable.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_list.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_map.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\comparators.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\equality.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\equality_map.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\equality_set.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\functions.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\iterable_extensions.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\iterable_zip.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\list_extensions.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\priority_queue.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\queue_list.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\union_set.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\union_set_controller.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\unmodifiable_wrappers.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\wrappers.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\blend\\blend.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\contrast\\contrast.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dislike\\dislike_analyzer.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\dynamic_color.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\dynamic_scheme.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\material_dynamic_colors.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\variant.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\cam16.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\hct.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\viewing_conditions.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\palettes\\core_palette.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\palettes\\tonal_palette.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_celebi.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_map.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_wsmeans.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_wu.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_content.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_expressive.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_fidelity.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_fruit_salad.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_monochrome.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_neutral.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_rainbow.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_tonal_spot.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_vibrant.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\score\\score.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\temperature\\temperature_cache.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\utils\\color_utils.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\utils\\math_utils.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\utils\\string_utils.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\alignment.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\basic_types.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\beveled_rectangle_border.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\binding.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\border_radius.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\borders.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\box_border.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\box_decoration.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\box_fit.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\box_shadow.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\circle_border.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\clip.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\colors.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\continuous_rectangle_border.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\debug.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\decoration.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\decoration_image.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\edge_insets.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\flutter_logo.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\fractional_offset.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\geometry.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\gradient.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\image_cache.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\image_decoder.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\image_provider.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\image_resolution.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\image_stream.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\inline_span.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\linear_border.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\matrix_utils.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\notched_shapes.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\oval_border.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\paint_utilities.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\placeholder_span.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\rounded_rectangle_border.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\shader_warm_up.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\shape_decoration.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\stadium_border.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\star_border.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\strut_style.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\text_painter.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\text_scaler.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\text_span.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\text_style.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\characters.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\extensions.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\constants.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\_web_image_info_io.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\_web_image_io.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\physics.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\_platform_selectable_region_context_menu_io.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\widgets\\_html_element_view_io.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\meta-1.16.0\\lib\\meta_meta.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\collisions.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\rendering\\decorator.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\camera.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\rendering.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\vector_math.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\core\\component_tree_root.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\devtools\\dev_tools_service.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\provider_interfaces.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\cache.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\extensions.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\flame.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\game_render_box.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\game_widget\\gesture_detector_builder.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\overlay_manager.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\input.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\geometry.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\text.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\flame_drag_adapter.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\tagged_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\tap_config.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\event.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\position_event.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\displacement_event.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\post_process.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\behaviors\\bounded_position_behavior.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\behaviors\\follow_behavior.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\behaviors\\viewport_aware_bounds_behavior.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\viewfinder.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\viewport.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\viewports\\fixed_resolution_viewport.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\viewports\\max_viewport.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\core\\component_render_context.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\effect_controller.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\move_by_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\move_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\move_to_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\experimental\\geometry\\shapes\\circle.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\experimental\\geometry\\shapes\\rectangle.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\experimental\\geometry\\shapes\\rounded_rectangle.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\experimental\\geometry\\shapes\\shape.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\collision_callbacks.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\hitboxes\\rectangle_hitbox.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\experimental.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\cache\\value_cache.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\ordered_set-8.0.0\\lib\\ordered_set.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\ordered_set-8.0.0\\lib\\read_only_ordered_set.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\layout.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\sprite_sheet.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\effects.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\palette.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\particle.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\nine_tile_box.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\cache\\images.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\parallax.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\particles.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\offset.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\rendering\\transform2d_decorator.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\math.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\sprite_animation_ticker.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\sprite_animation.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\sprite_batch.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\sprite.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\palette.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\math\\solve_quadratic.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\services\\_background_isolate_binary_messenger_io.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\shared_preferences-2.5.3\\lib\\src\\shared_preferences_devtools_extension_data.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\platform-3.1.6\\lib\\platform.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\platform_navigation_delegate.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\platform_ssl_auth_error.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\platform_webview_controller.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\platform_webview_cookie_manager.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\platform_webview_widget.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\types.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\webview_platform.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\_bitfield_io.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\_capabilities_io.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\_isolates_io.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\_platform_io.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\foundation\\_timeline_io.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\src\\get_application_id_real.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_directory.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_file.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_file_system.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_file_system_entity.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_link.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_random_access_file.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\directory.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\error_codes.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\file.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\file_system.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\file_system_entity.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\link.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\io.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_file_system.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\characters.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\internal_style.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\parsed_path.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\style\\posix.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\style\\url.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\style\\windows.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\ffi-2.1.4\\lib\\src\\allocation.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\ffi-2.1.4\\lib\\src\\arena.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\ffi-2.1.4\\lib\\src\\utf16.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\ffi-2.1.4\\lib\\src\\utf8.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\semantics\\binding.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\semantics\\debug.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\semantics\\semantics.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\semantics\\semantics_event.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\semantics\\semantics_service.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\utils.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_iterator.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\empty_unmodifiable_set.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\src\\contrast_curve.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\src\\tone_delta_pair.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\src\\hct_solver.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\src\\point_provider_lab.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\src\\point_provider.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\painting\\_network_image_io.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\characters_impl.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\physics\\clamped_simulation.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\physics\\friction_simulation.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\physics\\gravity_simulation.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\physics\\simulation.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\physics\\spring_simulation.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\physics\\tolerance.dart", "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter\\lib\\src\\physics\\utils.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\broadphase\\broadphase.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\broadphase\\prospect_pool.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\broadphase\\quadtree\\has_quadtree_collision_detection.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\broadphase\\quadtree\\quad_tree_broadphase.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\broadphase\\quadtree\\quadtree.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\broadphase\\quadtree\\quadtree_collision_detection.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\broadphase\\sweep\\sweep.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\collision_detection.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\collision_passthrough.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\hitboxes\\circle_hitbox.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\hitboxes\\composite_hitbox.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\hitboxes\\hitbox.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\hitboxes\\polygon_hitbox.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\hitboxes\\shape_hitbox.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\collisions\\standard_collision_detection.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\experimental\\raycast_result.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\rendering\\paint_decorator.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\rendering\\rotate3d_decorator.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\rendering\\shadow3d_decorator.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\viewports\\circular_viewport.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\viewports\\fixed_aspect_ratio_viewport.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\camera\\viewports\\fixed_size_viewport.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\aabb2.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\aabb3.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\colors.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\constants.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\error_helpers.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\frustum.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\intersection_result.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\matrix2.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\matrix3.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\matrix4.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\noise.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\obb3.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\opengl.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\plane.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\quad.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\quaternion.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\ray.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\sphere.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\triangle.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\utilities.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\vector.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\vector2.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\vector3.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math\\vector4.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\core\\recycled_queue.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\devtools\\connectors\\component_count_connector.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\devtools\\connectors\\component_snapshot_connector.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\devtools\\connectors\\component_tree_connector.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\devtools\\connectors\\debug_mode_connector.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\devtools\\connectors\\game_loop_connector.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\devtools\\connectors\\overlay_navigation_connector.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\devtools\\connectors\\position_component_attributes_connector.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\devtools\\dev_tools_connector.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\cache\\assets_cache.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\cache\\memory_cache.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\aabb.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\canvas.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\color.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\double.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\fragment_shader.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\image.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\list.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\matrix4.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\paint.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\path.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\picture.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\rect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\rectangle.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\extensions\\size.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\device.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\game\\game_loop.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\input\\button_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\input\\hud_button_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\input\\hud_margin_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\input\\sprite_button_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\geometry\\constants.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\geometry\\line.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\geometry\\line_segment.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\geometry\\polygon_ray_intersection.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\geometry\\ray2.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\geometry\\shape_intersections.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\common\\glyph.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\common\\line_metrics.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\common\\sprite_font.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\elements\\block_element.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\elements\\group_element.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\elements\\group_text_element.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\elements\\inline_text_element.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\elements\\rect_element.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\elements\\rrect_element.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\elements\\sprite_font_text_element.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\elements\\text_element.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\elements\\text_painter_text_element.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\block_node.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\bold_text_node.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\code_text_node.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\column_node.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\custom_text_node.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\document_root.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\group_text_node.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\header_node.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\inline_text_node.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\italic_text_node.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\paragraph_node.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\plain_text_node.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\strikethrough_text_node.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\text_block_node.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\nodes\\text_node.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\renderers\\sprite_font_renderer.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\renderers\\text_renderer.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\renderers\\text_renderer_factory.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\styles\\background_style.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\styles\\block_style.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\styles\\document_style.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\styles\\flame_text_style.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\styles\\inline_text_style.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\events\\messages\\location_context_event.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\post_process\\post_process.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\post_process\\post_process_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\callback_controller.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\curved_effect_controller.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\delayed_effect_controller.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\infinite_effect_controller.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\linear_effect_controller.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\pause_effect_controller.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\repeated_effect_controller.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\reverse_curved_effect_controller.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\reverse_linear_effect_controller.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\sequence_effect_controller.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\speed_effect_controller.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\effect_target.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\measurable_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\math\\random_fallback.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\experimental\\geometry\\shapes\\polygon.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\experimental\\column_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\experimental\\layout_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\experimental\\row_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\ordered_set-8.0.0\\lib\\comparing_ordered_set.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\ordered_set-8.0.0\\lib\\mapping_ordered_set.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\layout\\align_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\anchor_by_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\anchor_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\anchor_to_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\color_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\component_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\duration_effect_controller.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\mixins\\has_single_child_effect_controller.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\random_effect_controller.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\sine_effect_controller.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\controllers\\zigzag_effect_controller.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\function_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\glow_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\move_along_path_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\opacity_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\remove_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\rotate_around_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\rotate_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\scale_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\sequence_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\size_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\effects\\transform2d_effect.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\accelerated_particle.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\composed_particle.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\moving_particle.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\rotating_particle.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\scaled_particle.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\scaling_particle.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\translated_particle.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\circle_particle.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\component_particle.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\computed_particle.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\curved_particle.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\image_particle.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\paint_particle.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\sprite_animation_particle.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\particles\\sprite_particle.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\math\\solve_cubic.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\flame.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\image_composition.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\platform-3.1.6\\lib\\src\\interface\\local_platform.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\platform-3.1.6\\lib\\src\\interface\\platform.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\platform-3.1.6\\lib\\src\\testing\\fake_platform.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\http_auth_request.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\http_response_error.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\javascript_console_message.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\javascript_dialog_request.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\javascript_log_level.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\javascript_message.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\javascript_mode.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\load_request_params.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\navigation_decision.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\navigation_request.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\over_scroll_mode.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\platform_navigation_delegate_creation_params.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\platform_webview_controller_creation_params.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\platform_webview_cookie_manager_creation_params.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\platform_webview_permission_request.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\platform_webview_widget_creation_params.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\scroll_position_change.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\url_change.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\web_resource_error.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\web_resource_request.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\web_resource_response.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\webview_cookie.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\webview_credential.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\webview_flutter_platform_interface-2.13.1\\lib\\src\\types\\x509_certificate.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\error_codes_dart_io.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_directory.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_file.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_link.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\utils.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\grapheme_clusters\\breaks.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\grapheme_clusters\\constants.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\grapheme_clusters\\table.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\debug.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\cache\\matrix_pool.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\common\\utils.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\text\\styles\\overflow.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\math\\tmp_vector2.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\image_composition.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\ordered_set-8.0.0\\lib\\ordered_set_iterator.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\ordered_set-8.0.0\\lib\\queryable_ordered_set_impl.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\common.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_file_system_entity.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\debug\\child_counter_component.dart", "F:\\Projetos\\PUB_CACHE\\hosted\\pub.dev\\flame-1.29.0\\lib\\src\\components\\debug\\time_track_component.dart"], "outputs": ["F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\.dart_tool\\flutter_build\\0c8992056cfdfeb44696f2a8efa5014a\\app.dill", "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\.dart_tool\\flutter_build\\0c8992056cfdfeb44696f2a8efa5014a\\app.dill"]}