{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "F:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "F:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "F:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/bin/ctest.exe", "root": "F:/Dev/Android_SDK_HOME/sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-14ffe7b61ff89d3e684c.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-bbc7ba4bcf0548758479.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-ffe6187b15cd548e8e05.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-bbc7ba4bcf0548758479.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-ffe6187b15cd548e8e05.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-14ffe7b61ff89d3e684c.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}