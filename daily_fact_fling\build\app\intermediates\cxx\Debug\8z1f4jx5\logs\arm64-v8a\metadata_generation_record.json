[{"level_": 0, "message_": "Start JSON generation. Platform version: 21 min SDK version: arm64-v8a", "file_": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "profile|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\.cxx\\Debug\\8z1f4jx5\\arm64-v8a\\android_gradle_build.json due to:", "file_": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "profile|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "profile|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from 'F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\.cxx\\Debug\\8z1f4jx5\\arm64-v8a'", "file_": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "profile|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder 'F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\.cxx\\Debug\\8z1f4jx5\\arm64-v8a'", "file_": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "profile|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake @echo off\n\"F:\\\\Dev\\\\Android_SDK_HOME\\\\sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-Hf:\\\\Projetos\\\\fvm\\\\versions\\\\3.32.4\\\\packages\\\\flutter_tools\\\\gradle\\\\src\\\\main\\\\scripts\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=21\" ^\n  \"-DANDROID_PLATFORM=android-21\" ^\n  \"-DANDROID_ABI=arm64-v8a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a\" ^\n  \"-DANDROID_NDK=F:\\\\Dev\\\\Android_SDK_HOME\\\\sdk\\\\ndk\\\\26.3.11579264\" ^\n  \"-DCMAKE_ANDROID_NDK=F:\\\\Dev\\\\Android_SDK_HOME\\\\sdk\\\\ndk\\\\26.3.11579264\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=F:\\\\Dev\\\\Android_SDK_HOME\\\\sdk\\\\ndk\\\\26.3.11579264\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=F:\\\\Dev\\\\Android_SDK_HOME\\\\sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=F:\\\\Projetos\\\\02 - Dev\\\\daylyFacts\\\\daily_fact_fling\\\\build\\\\app\\\\intermediates\\\\cxx\\\\Debug\\\\8z1f4jx5\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=F:\\\\Projetos\\\\02 - Dev\\\\daylyFacts\\\\daily_fact_fling\\\\build\\\\app\\\\intermediates\\\\cxx\\\\Debug\\\\8z1f4jx5\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-BF:\\\\Projetos\\\\02 - Dev\\\\daylyFacts\\\\daily_fact_fling\\\\build\\\\.cxx\\\\Debug\\\\8z1f4jx5\\\\arm64-v8a\" ^\n  -GNinja ^\n  -Wno-dev ^\n  --no-warn-unused-cli\n", "file_": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "profile|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"F:\\\\Dev\\\\Android_SDK_HOME\\\\sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-Hf:\\\\Projetos\\\\fvm\\\\versions\\\\3.32.4\\\\packages\\\\flutter_tools\\\\gradle\\\\src\\\\main\\\\scripts\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=21\" ^\n  \"-DANDROID_PLATFORM=android-21\" ^\n  \"-DANDROID_ABI=arm64-v8a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a\" ^\n  \"-DANDROID_NDK=F:\\\\Dev\\\\Android_SDK_HOME\\\\sdk\\\\ndk\\\\26.3.11579264\" ^\n  \"-DCMAKE_ANDROID_NDK=F:\\\\Dev\\\\Android_SDK_HOME\\\\sdk\\\\ndk\\\\26.3.11579264\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=F:\\\\Dev\\\\Android_SDK_HOME\\\\sdk\\\\ndk\\\\26.3.11579264\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=F:\\\\Dev\\\\Android_SDK_HOME\\\\sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=F:\\\\Projetos\\\\02 - Dev\\\\daylyFacts\\\\daily_fact_fling\\\\build\\\\app\\\\intermediates\\\\cxx\\\\Debug\\\\8z1f4jx5\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=F:\\\\Projetos\\\\02 - Dev\\\\daylyFacts\\\\daily_fact_fling\\\\build\\\\app\\\\intermediates\\\\cxx\\\\Debug\\\\8z1f4jx5\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-BF:\\\\Projetos\\\\02 - Dev\\\\daylyFacts\\\\daily_fact_fling\\\\build\\\\.cxx\\\\Debug\\\\8z1f4jx5\\\\arm64-v8a\" ^\n  -GNinja ^\n  -Wno-dev ^\n  --no-warn-unused-cli\n", "file_": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "profile|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "profile|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\.cxx\\Debug\\8z1f4jx5\\arm64-v8a\\compile_commands.json.bin existed but not F:\\Projetos\\02 - Dev\\daylyFacts\\daily_fact_fling\\build\\.cxx\\Debug\\8z1f4jx5\\arm64-v8a\\compile_commands.json", "file_": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "profile|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "profile|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "f:\\Projetos\\fvm\\versions\\3.32.4\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "profile|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]