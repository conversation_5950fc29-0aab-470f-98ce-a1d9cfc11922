import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'game/daily_fact_fling_game.dart';
import 'ui/screens/main_menu_screen.dart';
import 'ui/themes/app_theme.dart';
import 'data/services/game_data_service.dart';

void main() {
  runApp(const DailyFactFlingApp());
}

class DailyFactFlingApp extends StatelessWidget {
  const DailyFactFlingApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        Provider<GameDataService>(create: (_) => GameDataService()),
        ChangeNotifierProvider<GameStateNotifier>(
          create: (_) => GameStateNotifier(),
        ),
      ],
      child: MaterialApp(
        title: 'Daily Fact Fling',
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: ThemeMode.system,
        home: const MainMenuScreen(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}

/// Game state notifier for managing game state across the app
class GameStateNotifier extends ChangeNotifier {
  DailyFactFlingGame? _game;
  bool _isGameActive = false;
  int _highScore = 0;
  bool _soundEnabled = true;
  bool _musicEnabled = true;

  // Getters
  DailyFactFlingGame? get game => _game;
  bool get isGameActive => _isGameActive;
  int get highScore => _highScore;
  bool get soundEnabled => _soundEnabled;
  bool get musicEnabled => _musicEnabled;

  /// Initialize a new game instance
  void initializeGame() {
    _game = DailyFactFlingGame();
    _isGameActive = true;
    notifyListeners();
  }

  /// End the current game
  void endGame() {
    _isGameActive = false;
    if (_game != null && _game!.score > _highScore) {
      _highScore = _game!.score;
      _saveHighScore();
    }
    notifyListeners();
  }

  /// Toggle sound on/off
  void toggleSound() {
    _soundEnabled = !_soundEnabled;
    notifyListeners();
    // TODO: Save to shared preferences
  }

  /// Toggle music on/off
  void toggleMusic() {
    _musicEnabled = !_musicEnabled;
    notifyListeners();
    // TODO: Save to shared preferences
  }

  /// Load saved settings
  Future<void> loadSettings() async {
    // TODO: Load from shared preferences
    // _highScore = await SharedPreferences...
    notifyListeners();
  }

  /// Save high score
  Future<void> _saveHighScore() async {
    // TODO: Save to shared preferences
  }
}
