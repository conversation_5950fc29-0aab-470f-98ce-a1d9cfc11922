# Flutter Game Development Guidelines

## Visão Geral
Este documento estabelece diretrizes e melhores práticas para desenvolvimento de jogos usando Flutter e Flame Engine, com foco em jogos hyper-casual, performance e monetização.

## 1. Arquitetura e Estrutura do Projeto

### 1.1 Estrutura de Diretórios Recomendada
```
lib/
├── main.dart
├── game/
│   ├── components/          # Componentes do jogo (player, enemies, etc.)
│   ├── screens/            # Telas do jogo (menu, gameplay, game over)
│   ├── systems/            # Sistemas do jogo (collision, scoring, etc.)
│   └── game_world.dart     # Classe principal do jogo
├── data/
│   ├── models/             # Modelos de dados
│   ├── repositories/       # Acesso a dados
│   └── services/           # Serviços (ads, analytics, etc.)
├── ui/
│   ├── widgets/            # Widgets reutilizáveis
│   ├── screens/            # Telas da UI (não-jogo)
│   └── themes/             # Temas e estilos
└── utils/
    ├── constants.dart      # Constantes do jogo
    ├── extensions.dart     # Extensões úteis
    └── helpers.dart        # Funções auxiliares
```

### 1.2 Separação de Responsabilidades
- **Game Layer**: Lógica do jogo usando Flame Engine
- **UI Layer**: Interface do usuário usando Flutter widgets
- **Data Layer**: Gerenciamento de dados e estado
- **Service Layer**: Integrações externas (ads, analytics, etc.)

## 2. Flame Engine - Melhores Práticas

### 2.1 Componentes do Jogo
```dart
// Exemplo de componente bem estruturado
class PlayerComponent extends SpriteComponent with HasKeyboardHandlerComponents, CollisionCallbacks {
  late final Vector2 velocity;
  late final double speed;
  
  @override
  Future<void> onLoad() async {
    sprite = await Sprite.load('player.png');
    size = Vector2(64, 64);
    velocity = Vector2.zero();
    speed = 200.0;
    
    add(RectangleHitbox());
  }
  
  @override
  void update(double dt) {
    super.update(dt);
    position += velocity * dt;
  }
}
```

### 2.2 Gerenciamento de Estado do Jogo
- Use `GameComponent` para componentes que precisam acessar o estado do jogo
- Implemente padrões como Observer para comunicação entre componentes
- Mantenha o estado do jogo centralizado quando possível

### 2.3 Performance
- Use `SpriteAnimationComponent` para animações eficientes
- Implemente object pooling para objetos que são criados/destruídos frequentemente
- Use `ParallaxComponent` para backgrounds com scroll
- Otimize colisões usando hitboxes apropriados

## 3. Integração Flutter + Flame

### 3.1 Overlay de UI
```dart
// Registrar overlays
game.overlays.addEntry('PauseMenu', (context, game) => PauseMenuWidget());
game.overlays.addEntry('GameHUD', (context, game) => GameHUDWidget());

// Mostrar/esconder overlays
game.overlays.add('GameHUD');
game.overlays.remove('PauseMenu');
```

### 3.2 Comunicação entre Flutter e Flame
```dart
// Use ValueNotifier para comunicação reativa
class GameState extends ChangeNotifier {
  int _score = 0;
  int get score => _score;
  
  void updateScore(int newScore) {
    _score = newScore;
    notifyListeners();
  }
}

// No widget Flutter
ValueListenableBuilder<int>(
  valueListenable: gameState.scoreNotifier,
  builder: (context, score, child) {
    return Text('Score: $score');
  },
)
```

## 4. Monetização com AdMob

### 4.1 Configuração
```dart
// Inicialização
await MobileAds.instance.initialize();

// IDs de teste (desenvolvimento)
const String testBannerAdUnitId = 'ca-app-pub-3940256099942544/6300978111';
const String testInterstitialAdUnitId = 'ca-app-pub-3940256099942544/1033173712';
const String testRewardedAdUnitId = 'ca-app-pub-3940256099942544/5224354917';
```

### 4.2 Implementação de Anúncios
```dart
class AdService {
  InterstitialAd? _interstitialAd;
  RewardedAd? _rewardedAd;
  
  // Carregar anúncio intersticial
  void loadInterstitialAd() {
    InterstitialAd.load(
      adUnitId: testInterstitialAdUnitId,
      request: const AdRequest(),
      adLoadCallback: InterstitialAdLoadCallback(
        onAdLoaded: (ad) => _interstitialAd = ad,
        onAdFailedToLoad: (error) => print('Failed to load: $error'),
      ),
    );
  }
  
  // Mostrar anúncio recompensado
  void showRewardedAd(VoidCallback onRewarded) {
    _rewardedAd?.show(
      onUserEarnedReward: (ad, reward) => onRewarded(),
    );
  }
}
```

### 4.3 Estratégias de Monetização
- **Intersticiais**: A cada 3-5 game overs ou após sessões longas
- **Recompensados**: Para continuar o jogo, dobrar pontuação, ou desbloquear conteúdo
- **Banner**: Em menus, evitar durante gameplay ativo

## 5. Gerenciamento de Dados

### 5.1 Armazenamento Local
```dart
// Usando shared_preferences para dados simples
class GameDataService {
  static const String _highScoreKey = 'high_score';
  
  Future<int> getHighScore() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getInt(_highScoreKey) ?? 0;
  }
  
  Future<void> saveHighScore(int score) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_highScoreKey, score);
  }
}
```

### 5.2 Conteúdo do Jogo
```dart
// Modelo para fatos do jogo
class GameFact {
  final String id;
  final String text;
  final List<String> categories;
  final String correctCategory;
  
  const GameFact({
    required this.id,
    required this.text,
    required this.categories,
    required this.correctCategory,
  });
  
  factory GameFact.fromJson(Map<String, dynamic> json) {
    return GameFact(
      id: json['id'],
      text: json['text'],
      categories: List<String>.from(json['categories']),
      correctCategory: json['correct_category'],
    );
  }
}
```

## 6. Performance e Otimização

### 6.1 Diretrizes Gerais
- Mantenha 60 FPS como meta
- Use `flutter analyze` e `flutter test` regularmente
- Profile com `flutter run --profile`
- Otimize assets (compressão de imagens, formatos adequados)

### 6.2 Otimizações Específicas para Jogos
```dart
// Object pooling para projéteis
class BulletPool {
  final List<BulletComponent> _available = [];
  final List<BulletComponent> _inUse = [];
  
  BulletComponent getBullet() {
    if (_available.isEmpty) {
      return BulletComponent();
    }
    final bullet = _available.removeLast();
    _inUse.add(bullet);
    return bullet;
  }
  
  void returnBullet(BulletComponent bullet) {
    _inUse.remove(bullet);
    _available.add(bullet);
    bullet.reset();
  }
}
```

### 6.3 Gerenciamento de Memória
- Dispose adequadamente de recursos (controllers, streams, etc.)
- Use `late` para inicialização tardia quando apropriado
- Evite vazamentos de memória em listeners e callbacks

## 7. Testes e Qualidade

### 7.1 Testes Unitários
```dart
// Teste para lógica de pontuação
void main() {
  group('ScoreSystem', () {
    test('should increase score correctly', () {
      final scoreSystem = ScoreSystem();
      scoreSystem.addPoints(100);
      expect(scoreSystem.currentScore, 100);
    });
  });
}
```

### 7.2 Testes de Widget
```dart
// Teste para UI do jogo
void main() {
  testWidgets('GameHUD displays score correctly', (tester) async {
    await tester.pumpWidget(
      MaterialApp(
        home: GameHUD(score: 1500),
      ),
    );
    
    expect(find.text('Score: 1500'), findsOneWidget);
  });
}
```

## 8. Deployment e Distribuição

### 8.1 Preparação para Release
- Configure IDs de produção do AdMob
- Otimize assets e reduza tamanho do APK/IPA
- Configure signing para Android e iOS
- Teste em dispositivos reais

### 8.2 Checklist de Release
- [ ] Testes em múltiplos dispositivos
- [ ] Performance verificada (60 FPS)
- [ ] Anúncios funcionando corretamente
- [ ] Dados persistindo adequadamente
- [ ] Ícones e screenshots preparados
- [ ] Descrições da loja escritas
- [ ] Políticas de privacidade atualizadas

## 9. Manutenção e Evolução

### 9.1 Versionamento
- Use semantic versioning (1.0.0, 1.1.0, 1.1.1)
- Mantenha changelog atualizado
- Planeje updates regulares de conteúdo

### 9.2 Analytics e Feedback
- Implemente analytics para entender comportamento do usuário
- Monitore crashes e erros
- Colete feedback dos usuários
- Analise métricas de monetização

## 10. Recursos e Ferramentas Recomendadas

### 10.1 Packages Essenciais
- `flame`: Engine de jogo
- `google_mobile_ads`: Monetização
- `shared_preferences`: Armazenamento local
- `audioplayers`: Efeitos sonoros
- `flutter_launcher_icons`: Ícones do app

### 10.2 Ferramentas de Desenvolvimento
- Flutter DevTools para debugging
- Firebase Crashlytics para monitoramento
- Codemagic ou GitHub Actions para CI/CD
- Figma ou Adobe XD para design

---

**Nota**: Este documento deve ser atualizado conforme novas versões do Flutter e Flame são lançadas, e baseado na experiência adquirida durante o desenvolvimento.
